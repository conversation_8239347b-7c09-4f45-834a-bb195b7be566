"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSHTerminalManager = void 0;
const vscode = require("vscode");
const advancedShellIntegration_1 = require("./advancedShellIntegration");
const shellIntegrationHelper_1 = require("./shellIntegrationHelper");
/**
 * SSH 终端管理器
 * 负责创建SSH连接、执行命令和管理终端状态
 */
class SSHTerminalManager {
    constructor(outputChannel) {
        this.isConnected = false;
        this.outputChannel = outputChannel;
        this.shellHelper = new shellIntegrationHelper_1.ShellIntegrationHelper(outputChannel);
        this.advancedShellIntegration = new advancedShellIntegration_1.AdvancedShellIntegration(outputChannel);
        this.commandTimeout = vscode.workspace.getConfiguration('sshDemo').get('commandTimeout', 30000);
    }
    /**
     * 创建SSH连接
     */
    async connectSSH(host, username) {
        try {
            this.log(`开始连接SSH: ${username}@${host}`);
            // 创建新的终端
            this.terminal = vscode.window.createTerminal({
                name: `SSH Demo - ${host}`,
                shellPath: process.platform === 'win32' ? 'powershell.exe' : '/bin/bash',
                shellArgs: [],
                env: {
                    // 启用Shell Integration
                    'VSCODE_SHELL_INTEGRATION': '1'
                },
                location: vscode.TerminalLocation.Editor
            });
            // 显示终端
            this.terminal.show();
            // 设置高级Shell Integration监听
            this.advancedShellIntegration.setTerminal(this.terminal);
            // 构建SSH命令
            const sshCommand = `ssh ${username}@${host}`;
            this.log(`执行SSH命令: ${sshCommand}`);
            // 发送SSH命令
            this.terminal.sendText(sshCommand);
            // 等待SSH连接提示出现后，再弹出密码输入框
            await this.waitForSSHPrompt();
            // 等待用户输入密码，默认密码是InSec@888
            const password = await vscode.window.showInputBox({
                prompt: '请输入SSH密码',
                password: true,
                placeHolder: '默认密码: InSec@888'
            }) || 'InSec@888';
            // 发送密码
            this.terminal.sendText(password);
            this.isConnected = true;
            this.log(`SSH连接流程已启动，请在终端中手动输入密码`);
            return true;
        }
        catch (error) {
            this.log(`SSH连接失败: ${error}`);
            return false;
        }
    }
    /**
     * 在SSH终端中执行命令
     */
    async executeCommand(command) {
        if (!this.terminal || !this.isConnected) {
            const error = 'SSH终端未连接';
            this.log(error);
            return { success: false, output: '', error, timedOut: false };
        }
        this.log(`执行命令: ${command}`);
        try {
            // 使用高级Shell Integration执行命令
            const result = await this.advancedShellIntegration.executeCommandWithIntegration(command, this.commandTimeout);
            this.log(`命令执行结果: 成功=${result.success}, 超时=${result.timedOut}, Shell Integration=${result.shellIntegrationDetected}`);
            return {
                success: result.success,
                output: result.output,
                error: result.error,
                timedOut: result.timedOut
            };
        }
        catch (error) {
            const errorMsg = `命令执行失败: ${error}`;
            this.log(errorMsg);
            return { success: false, output: '', error: errorMsg, timedOut: false };
        }
    }
    /**
     * 断开SSH连接
     */
    disconnect() {
        if (this.terminal) {
            this.log('断开SSH连接');
            this.terminal.dispose();
            this.terminal = undefined;
            this.isConnected = false;
        }
    }
    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return this.isConnected;
    }
    /**
     * 获取调试信息
     */
    getDebugInfo() {
        const advancedDebugInfo = this.advancedShellIntegration.getDebugInfo();
        return {
            isConnected: this.isConnected,
            hasTerminal: !!this.terminal,
            terminalName: this.terminal?.name,
            commandTimeout: this.commandTimeout,
            timestamp: new Date().toISOString(),
            advancedShellIntegration: advancedDebugInfo
        };
    }
    /**
     * 等待SSH连接提示
     */
    async waitForSSHPrompt() {
        // 给SSH命令一些时间来显示连接提示
        return new Promise(resolve => {
            setTimeout(() => {
                this.log('SSH连接提示应该已出现，准备弹出密码输入框');
                resolve();
            }, 3000); // 增加等待时间，确保SSH提示出现
        });
    }
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * 记录日志
     */
    log(message) {
        const timestamp = new Date().toISOString();
        this.outputChannel.appendLine(`[${timestamp}] SSHManager: ${message}`);
    }
}
exports.SSHTerminalManager = SSHTerminalManager;
//# sourceMappingURL=sshTerminalManager.js.map