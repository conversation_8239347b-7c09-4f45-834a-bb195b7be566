{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6DAA0D;AAE1D,IAAI,aAAmC,CAAC;AACxC,IAAI,UAA8B,CAAC;AAEnC;;GAEG;AACH,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,SAAS;IACT,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;IACpE,aAAa,CAAC,IAAI,EAAE,CAAC;IACrB,aAAa,CAAC,UAAU,CAAC,uCAAuC,CAAC,CAAC;IAElE,WAAW;IACX,UAAU,GAAG,IAAI,uCAAkB,CAAC,aAAa,CAAC,CAAC;IAEnD,kBAAkB;IAClB,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACrF,IAAI;YACA,aAAa,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;YAErD,OAAO;YACP,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAS,aAAa,EAAE,eAAe,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAS,aAAa,EAAE,MAAM,CAAC,CAAC;YAE9D,eAAe;YACf,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC1C,MAAM,EAAE,YAAY;gBACpB,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,mBAAmB;aACnC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE;gBACP,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;gBAClD,OAAO;aACV;YAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC9C,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,WAAW;gBAClB,WAAW,EAAE,UAAU;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE;gBACX,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;gBACjD,OAAO;aACV;YAED,OAAO;YACP,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,UAAU;gBACjB,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAClB,aAAa;gBACb,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;gBACzD,aAAa,CAAC,UAAU,CAAC,SAAS,QAAQ,IAAI,IAAI,EAAE,CAAC,CAAC;gBAEtD,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAE9D,IAAI,CAAC,SAAS,EAAE;oBACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;oBAC1C,OAAO;iBACV;gBAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACjE,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAEtC,oBAAoB;gBACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBACjE,aAAa,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;gBAE7C,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAE3D,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAEtD,OAAO;gBACP,IAAI,MAAM,CAAC,OAAO,EAAE;oBAChB,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;oBAC3C,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACxC,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;oBAEzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,sBAAsB,EACtB,MAAM,CACT,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBACf,IAAI,SAAS,KAAK,MAAM,EAAE;4BACtB,aAAa,CAAC,IAAI,EAAE,CAAC;yBACxB;oBACL,CAAC,CAAC,CAAC;iBACN;qBAAM;oBACH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC;oBACvE,aAAa,CAAC,UAAU,CAAC,WAAW,QAAQ,EAAE,CAAC,CAAC;oBAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,QAAQ,EAAE,CAAC,CAAC;iBACzD;gBAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,QAAQ,GAAG,SAAS,KAAK,EAAE,CAAC;YAClC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SAC5C;IACL,CAAC,CAAC,CAAC;IAEH,cAAc;IACd,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAChF,IAAI;YACA,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAErD,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC3C,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACpC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC3C,aAAa,CAAC,IAAI,EAAE,CAAC;YAErB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;SACxD;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;SACxD;IACL,CAAC,CAAC,CAAC;IAEH,WAAW;IACX,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3C,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,SAAS;IACT,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE;QACpE,IAAI,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACvC,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC5C,aAAa;SAChB;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACtC,aAAa,CAAC,UAAU,CAAC,2CAA2C,CAAC,CAAC;AAC1E,CAAC;AAvID,4BAuIC;AAED;;GAEG;AACH,SAAgB,UAAU;IACtB,IAAI,UAAU,EAAE;QACZ,UAAU,CAAC,UAAU,EAAE,CAAC;KAC3B;IAED,IAAI,aAAa,EAAE;QACf,aAAa,CAAC,UAAU,CAAC,uCAAuC,CAAC,CAAC;QAClE,aAAa,CAAC,OAAO,EAAE,CAAC;KAC3B;IAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;AACpD,CAAC;AAXD,gCAWC"}