{"version": 3, "file": "advancedShellIntegration.js", "sourceRoot": "", "sources": ["../src/advancedShellIntegration.ts"], "names": [], "mappings": ";;;AACA,qEAAkE;AAElE;;;GAGG;AACH,MAAa,wBAAwB;IAQjC,YAAY,aAAmC;QAJvC,iBAAY,GAAY,KAAK,CAAC;QAC9B,iBAAY,GAAW,EAAE,CAAC;QAC1B,mBAAc,GAAW,CAAC,CAAC;QAG/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAsB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,QAAyB;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,6BAA6B,CACtC,OAAe,EACf,YAAoB,KAAK;QAQzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,IAAI,gBAAgB,GAAG,KAAK,CAAC;YAC7B,IAAI,wBAAwB,GAAG,KAAK,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,OAAO;YACP,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,EAAE;oBACX,QAAQ,GAAG,IAAI,CAAC;oBAChB,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CAAC,WAAW,SAAS,IAAI,CAAC,CAAC;oBACnC,OAAO,CAAC;wBACJ,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,IAAI,CAAC,YAAY;wBACzB,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,IAAI;wBACd,wBAAwB;qBAC3B,CAAC,CAAC;iBACN;YACL,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,WAAW;YACX,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC5B,IAAI,QAAQ;oBAAE,OAAO;gBAErB,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;gBAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEjC,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAEjE,wBAAwB;gBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAEhE,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,iBAAiB,EAAE;oBACvD,wBAAwB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,cAAc,EAAE;wBACjB,cAAc,GAAG,IAAI,CAAC;wBACtB,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;qBAC1C;iBACJ;gBAED,IAAI,QAAQ,CAAC,YAAY,IAAI,cAAc,EAAE;oBACzC,gBAAgB,GAAG,IAAI,CAAC;oBACxB,wBAAwB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAEvC,IAAI,CAAC,QAAQ,EAAE;wBACX,QAAQ,GAAG,IAAI,CAAC;wBAChB,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;wBAEtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACrF,OAAO,CAAC;4BACJ,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,WAAW;4BACnB,QAAQ,EAAE,KAAK;4BACf,wBAAwB,EAAE,IAAI;yBACjC,CAAC,CAAC;qBACN;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO;YACP,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAErB,2CAA2C;YAC3C,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;gBACnC,IAAI,QAAQ,EAAE;oBACV,aAAa,CAAC,aAAa,CAAC,CAAC;oBAC7B,OAAO;iBACV;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBACvC,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;gBAE7D,+CAA+C;gBAC/C,IAAI,OAAO,GAAG,IAAI,IAAI,CAAC,wBAAwB,IAAI,mBAAmB,GAAG,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC3G,IAAI,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;oBAE/C,IAAI,CAAC,QAAQ,EAAE;wBACX,QAAQ,GAAG,IAAI,CAAC;wBAChB,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC5B,aAAa,CAAC,aAAa,CAAC,CAAC;wBAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;wBAEtB,OAAO,CAAC;4BACJ,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,IAAI,CAAC,YAAY;4BACzB,QAAQ,EAAE,KAAK;4BACf,wBAAwB,EAAE,KAAK;yBAClC,CAAC,CAAC;qBACN;iBACJ;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAkC;QACtD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAErB,0CAA0C;QAC1C,4BAA4B;QAC5B,IAAI,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SACxB;IACL,CAAC;IAED;;;OAGG;IACK,gCAAgC,CAAC,QAAkC;QACvE,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,aAAa,CAAC,kBAAkB,CAAC,CAAC;gBAClC,OAAO;aACV;YAED,YAAY;YACZ,IAAI,UAAU,GAAG,EAAE,CAAC;YAEpB,QAAQ,cAAc,EAAE;gBACpB,KAAK,CAAC;oBACF,0BAA0B;oBAC1B,UAAU,GAAG,gBAAgB,CAAC;oBAC9B,MAAM;gBACV,KAAK,CAAC;oBACF,UAAU,GAAG,YAAY,CAAC;oBAC1B,MAAM;gBACV,KAAK,CAAC;oBACF,UAAU,GAAG,kDAAkD,CAAC;oBAChE,MAAM;gBACV,KAAK,CAAC;oBACF,UAAU,GAAG,qDAAqD,CAAC;oBACnE,MAAM;gBACV,KAAK,CAAC;oBACF,UAAU,GAAG,sDAAsD,CAAC;oBACpE,MAAM;gBACV,KAAK,CAAC;oBACF,UAAU,GAAG,uDAAuD,CAAC;oBACrE,MAAM;gBACV,KAAK,CAAC;oBACF,UAAU,GAAG,2DAA2D,CAAC;oBACzE,MAAM;gBACV,KAAK,CAAC;oBACF,0BAA0B;oBAC1B,UAAU,GAAG,gBAAgB,CAAC;oBAC9B,aAAa,CAAC,kBAAkB,CAAC,CAAC;oBAClC,MAAM;aACb;YAED,IAAI,UAAU,EAAE;gBACZ,QAAQ,CAAC,UAAU,CAAC,CAAC;aACxB;YAED,cAAc,EAAE,CAAC;QACrB,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,UAAU;QACd,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YAC5C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;YAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;SACpC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,OAAe;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,SAAS,+BAA+B,OAAO,EAAE,CAAC,CAAC;IACzF,CAAC;CACJ;AAhQD,4DAgQC"}