{"version": 3, "file": "advancedShellIntegration.js", "sourceRoot": "", "sources": ["../src/advancedShellIntegration.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,qEAAkE;AAElE;;;GAGG;AACH,MAAa,wBAAwB;IASjC,YAAY,aAAmC;QALvC,iBAAY,GAAY,KAAK,CAAC;QAC9B,iBAAY,GAAW,EAAE,CAAC;QAC1B,mBAAc,GAAW,CAAC,CAAC;QAI/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAsB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,QAAyB;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,6BAA6B,CACtC,OAAe,EACf,YAAoB,KAAK;QAQzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,cAAc,GAAG,KAAK,CAAC;YAE3B,IAAI,wBAAwB,GAAG,KAAK,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,OAAO;YACP,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,EAAE;oBACX,QAAQ,GAAG,IAAI,CAAC;oBAChB,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CAAC,WAAW,SAAS,IAAI,CAAC,CAAC;oBACnC,OAAO,CAAC;wBACJ,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,IAAI,CAAC,YAAY;wBACzB,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,IAAI;wBACd,wBAAwB;qBAC3B,CAAC,CAAC;iBACN;YACL,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,WAAW;YACX,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC5B,IAAI,QAAQ;oBAAE,OAAO;gBAErB,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;gBAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEjC,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAEjE,wBAAwB;gBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAEhE,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,iBAAiB,EAAE;oBACvD,wBAAwB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,cAAc,EAAE;wBACjB,cAAc,GAAG,IAAI,CAAC;wBACtB,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;qBAC1C;iBACJ;gBAED,IAAI,QAAQ,CAAC,YAAY,IAAI,cAAc,EAAE;oBACzC,wBAAwB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAEvC,IAAI,CAAC,QAAQ,EAAE;wBACX,QAAQ,GAAG,IAAI,CAAC;wBAChB,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;wBAEtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACrF,OAAO,CAAC;4BACJ,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,WAAW;4BACnB,QAAQ,EAAE,KAAK;4BACf,wBAAwB,EAAE,IAAI;yBACjC,CAAC,CAAC;qBACN;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO;YACP,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAErB,0BAA0B;YAC1B,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;gBACzC,IAAI,QAAQ,EAAE;oBACV,aAAa,CAAC,aAAa,CAAC,CAAC;oBAC7B,OAAO;iBACV;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAEvC,0CAA0C;gBAC1C,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,wBAAwB,EAAE;oBAC9C,IAAI,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;oBAE/C,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC3D,+BAA+B,EAC/B,QAAQ,EACR,QAAQ,EACR,IAAI,CACP,CAAC;oBAEF,IAAI,YAAY,KAAK,QAAQ,EAAE;wBAC3B,IAAI,CAAC,QAAQ,EAAE;4BACX,QAAQ,GAAG,IAAI,CAAC;4BAChB,YAAY,CAAC,aAAa,CAAC,CAAC;4BAC5B,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;4BAEtB,aAAa;4BACb,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gCAChD,MAAM,EAAE,oBAAoB;gCAC5B,WAAW,EAAE,mBAAmB;6BACnC,CAAC,CAAC;4BAEH,OAAO,CAAC;gCACJ,OAAO,EAAE,IAAI;gCACb,MAAM,EAAE,UAAU,IAAI,WAAW;gCACjC,QAAQ,EAAE,KAAK;gCACf,wBAAwB,EAAE,KAAK;6BAClC,CAAC,CAAC;yBACN;qBACJ;yBAAM,IAAI,YAAY,KAAK,IAAI,EAAE;wBAC9B,IAAI,CAAC,QAAQ,EAAE;4BACX,QAAQ,GAAG,IAAI,CAAC;4BAChB,YAAY,CAAC,aAAa,CAAC,CAAC;4BAC5B,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;4BAEtB,OAAO,CAAC;gCACJ,OAAO,EAAE,KAAK;gCACd,MAAM,EAAE,EAAE;gCACV,KAAK,EAAE,WAAW;gCAClB,QAAQ,EAAE,KAAK;gCACf,wBAAwB,EAAE,KAAK;6BAClC,CAAC,CAAC;yBACN;qBACJ;oBACD,sBAAsB;iBACzB;YACL,CAAC,EAAE,IAAI,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAkC;QACtD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAErB,cAAc;QACd,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SACxB;IACL,CAAC;IAED;;;OAGG;IACK,4BAA4B,CAAC,QAAkC;QACnE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEvB,oCAAoC;QACpC,yBAAyB;QAEzB,kBAAkB;QAClB,MAAM,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACpB,aAAa,CAAC,kBAAkB,CAAC,CAAC;gBAClC,OAAO;aACV;YAED,aAAa;YACb,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;gBACzD,oBAAoB;gBACpB,qBAAqB;gBACrB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;aACzC;iBAAM;gBACH,QAAQ;gBACR,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAClB,aAAa,CAAC,kBAAkB,CAAC,CAAC;gBAClC,IAAI,CAAC,cAAc,EAAE,CAAC;aACzB;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,eAAe;QACf,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE;YACjE,IAAI,cAAc,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAClC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACtB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,UAAU,CAAC,OAAO,EAAE,CAAC;aACxB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACK,sBAAsB,CAAC,QAAkC;QAC7D,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QAEnB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;QAEjD,sBAAsB;QACtB,IAAI,OAAO,GAAG,IAAI,EAAE;YAChB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEjC,8BAA8B;YAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACvD,IAAI,eAAe,EAAE;gBACjB,QAAQ,CAAC,eAAe,CAAC,CAAC;aAC7B;SACJ;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC3B,8BAA8B;QAC9B,uBAAuB;QACvB,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QACxC,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACK,UAAU;QACd,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YAC5C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;YAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;SACpC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,OAAe;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,SAAS,+BAA+B,OAAO,EAAE,CAAC,CAAC;IACzF,CAAC;CACJ;AAtTD,4DAsTC"}