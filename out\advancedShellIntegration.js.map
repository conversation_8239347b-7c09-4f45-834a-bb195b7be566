{"version": 3, "file": "advancedShellIntegration.js", "sourceRoot": "", "sources": ["../src/advancedShellIntegration.ts"], "names": [], "mappings": ";;;AAAA,2BAA2B;AAC3B,iCAAiC;AACjC,qEAAkE;AAClE,uCAAuC,CAAC,uBAAuB;AAE/D;;;GAGG;AACH,MAAa,wBAAwB;IASjC,YAAY,aAAmC;QALvC,iBAAY,GAAY,KAAK,CAAC;QAC9B,iBAAY,GAAW,EAAE,CAAC;QAC1B,mBAAc,GAAW,CAAC,CAAC;QAI/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAsB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,QAAyB;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,WAAW,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,6BAA6B,CACtC,OAAe,EACf,YAAoB,KAAK;QAQzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,cAAc,GAAG,KAAK,CAAC;YAE3B,IAAI,wBAAwB,GAAG,KAAK,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,OAAO;YACP,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;gBAClC,IAAI,CAAC,QAAQ,EAAE;oBACX,QAAQ,GAAG,IAAI,CAAC;oBAChB,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,GAAG,CAAC,WAAW,SAAS,IAAI,CAAC,CAAC;oBACnC,OAAO,CAAC;wBACJ,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,IAAI,CAAC,YAAY;wBACzB,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,IAAI;wBACd,wBAAwB;qBAC3B,CAAC,CAAC;iBACN;YACL,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,WAAW;YACX,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC5B,IAAI,QAAQ;oBAAE,OAAO;gBAErB,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC;gBAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEjC,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAEjE,wBAAwB;gBACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAEhE,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,iBAAiB,EAAE;oBACvD,wBAAwB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,cAAc,EAAE;wBACjB,cAAc,GAAG,IAAI,CAAC;wBACtB,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;qBAC1C;iBACJ;gBAED,IAAI,QAAQ,CAAC,YAAY,IAAI,cAAc,EAAE;oBACzC,wBAAwB,GAAG,IAAI,CAAC;oBAChC,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;oBAEvC,IAAI,CAAC,QAAQ,EAAE;wBACX,QAAQ,GAAG,IAAI,CAAC;wBAChB,YAAY,CAAC,aAAa,CAAC,CAAC;wBAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;wBAEtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACrF,OAAO,CAAC;4BACJ,OAAO,EAAE,IAAI;4BACb,MAAM,EAAE,WAAW;4BACnB,QAAQ,EAAE,KAAK;4BACf,wBAAwB,EAAE,IAAI;yBACjC,CAAC,CAAC;qBACN;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO;YACP,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAErB,0BAA0B;YAC1B,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;gBACzC,IAAI,QAAQ,EAAE;oBACV,aAAa,CAAC,aAAa,CAAC,CAAC;oBAC7B,OAAO;iBACV;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAEvC,0CAA0C;gBAC1C,IAAI,OAAO,GAAG,KAAK,IAAI,CAAC,wBAAwB,EAAE;oBAC9C,IAAI,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;oBAE/C,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC3D,+BAA+B,EAC/B,QAAQ,EACR,QAAQ,EACR,IAAI,CACP,CAAC;oBAEF,IAAI,YAAY,KAAK,QAAQ,EAAE;wBAC3B,IAAI,CAAC,QAAQ,EAAE;4BACX,QAAQ,GAAG,IAAI,CAAC;4BAChB,YAAY,CAAC,aAAa,CAAC,CAAC;4BAC5B,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;4BAEtB,aAAa;4BACb,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gCAChD,MAAM,EAAE,oBAAoB;gCAC5B,WAAW,EAAE,mBAAmB;6BACnC,CAAC,CAAC;4BAEH,OAAO,CAAC;gCACJ,OAAO,EAAE,IAAI;gCACb,MAAM,EAAE,UAAU,IAAI,WAAW;gCACjC,QAAQ,EAAE,KAAK;gCACf,wBAAwB,EAAE,KAAK;6BAClC,CAAC,CAAC;yBACN;qBACJ;yBAAM,IAAI,YAAY,KAAK,IAAI,EAAE;wBAC9B,IAAI,CAAC,QAAQ,EAAE;4BACX,QAAQ,GAAG,IAAI,CAAC;4BAChB,YAAY,CAAC,aAAa,CAAC,CAAC;4BAC5B,aAAa,CAAC,aAAa,CAAC,CAAC;4BAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;4BAEtB,OAAO,CAAC;gCACJ,OAAO,EAAE,KAAK;gCACd,MAAM,EAAE,EAAE;gCACV,KAAK,EAAE,WAAW;gCAClB,QAAQ,EAAE,KAAK;gCACf,wBAAwB,EAAE,KAAK;6BAClC,CAAC,CAAC;yBACN;qBACJ;oBACD,sBAAsB;iBACzB;YACL,CAAC,EAAE,IAAI,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAkC;QACtD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,cAAc,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAErB,cAAc;QACd,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SACxB;IACL,CAAC;IAED;;;OAGG;IACK,4BAA4B,CAAC,QAAkC;QACnE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEvB,iCAAiC;QACjC,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,CAAC;QAEjD,gBAAgB;QAChB,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE;YACjE,IAAI,cAAc,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAClC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBACtB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,UAAU,CAAC,OAAO,EAAE,CAAC;aACxB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,iCAAiC,CAAC,QAAkC;QACxE,WAAW;QACX,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAE5F,YAAY;QACZ,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAS,aAAa,EAAE,aAAa,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAS,aAAa,EAAE,MAAM,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAS,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAEpE,cAAc;QACd,IAAI,UAAU,GAAQ,IAAI,CAAC;QAE3B,4CAA4C;QAC5C,kCAAkC;QAClC,IAAI,UAAkB,CAAC;QAEvB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;YAC9B,uCAAuC;YACvC,qBAAqB;YACrB,uBAAuB;YACvB,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAEzC,6BAA6B;YAC7B,MAAM,cAAc,GAAG,GAAG,eAAe,uBAAuB,CAAC;YACjE,MAAM,aAAa,GAAG;eACnB,QAAQ;;uEAEgD,QAAQ;;;0CAGrC,IAAI;;;;;;;;;;;;;;CAc7C,CAAC;YAEU,YAAY;YACZ,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YAEhD,iBAAiB;YACjB,UAAU,GAAG,6CAA6C,cAAc,GAAG,CAAC;SAC/E;aAAM;YACH,uBAAuB;YACvB,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAEvC,iBAAiB;YACjB,MAAM,cAAc,GAAG,GAAG,eAAe,sBAAsB,CAAC;YAChE,MAAM,aAAa,GAAG;;;YAGtB,QAAQ,IAAI,IAAI;;;;0BAIF,QAAQ;;;;;;;;;;;;;;;;;CAiBjC,CAAC;YAEU,mBAAmB;YACnB,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,EAAE,CAAC,aAAa,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YAChD,EAAE,CAAC,SAAS,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YAEpC,aAAa;YACb,UAAU,GAAG,cAAc,CAAC;SAC/B;QAED,IAAI,CAAC,GAAG,CAAC,YAAY,UAAU,EAAE,CAAC,CAAC;QAEnC,IAAI;YACA,UAAU;YACV,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,EAAE;gBACrC,GAAG,EAAE,eAAe;gBACpB,KAAK,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,SAAS;YACT,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,IAAI,CAAC,GAAG,CAAC,UAAU,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAExC,mBAAmB;gBACnB,MAAM,mBAAmB,GAAG,+CAA+C,CAAC;gBAC5E,MAAM,qBAAqB,GAAG,gBAAgB,CAAC;gBAC/C,MAAM,YAAY,GAAG,+DAA+D,CAAC;gBAErF,IAAI,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACtC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBACvB,QAAQ,CAAC,SAAS,CAAC,CAAC;iBACvB;qBAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBAC/C,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACpB,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnB,SAAS;oBACT,IAAI,UAAU,CAAC,KAAK,EAAE;wBAClB,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC;wBACxC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;qBACvB;iBACJ;qBAAM,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACtC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBACvB,QAAQ,CAAC,YAAY,UAAU,EAAE,CAAC,CAAC;iBACtC;gBAED,eAAe;gBACf,QAAQ,CAAC,UAAU,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,GAAG,CAAC,UAAU,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACvC,QAAQ,CAAC,OAAO,SAAS,EAAE,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAY,EAAE,EAAE;gBACpC,IAAI,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;gBAEjC,SAAS;gBACT,IAAI;oBACA,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;oBACzB,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;wBAC/C,CAAC,CAAC,GAAG,eAAe,uBAAuB;wBAC3C,CAAC,CAAC,GAAG,eAAe,sBAAsB,CAAC;oBAE/C,IAAI,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;wBAC/B,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;wBAC9B,IAAI,CAAC,GAAG,CAAC,YAAY,cAAc,EAAE,CAAC,CAAC;qBAC1C;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACZ,IAAI,CAAC,GAAG,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;iBAClC;YACL,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;gBACpC,IAAI,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtC,QAAQ,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,EAAE,CAAC,CAAC;SACnC;IACL,CAAC;IAED;;;OAGG;IACK,sBAAsB,CAAC,QAAkC;QAC7D,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QAEnB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;QAEjD,sBAAsB;QACtB,IAAI,OAAO,GAAG,IAAI,EAAE;YAChB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEjC,8BAA8B;YAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACvD,IAAI,eAAe,EAAE;gBACjB,QAAQ,CAAC,eAAe,CAAC,CAAC;aAC7B;SACJ;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC3B,8BAA8B;QAC9B,uBAAuB;QACvB,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QACxC,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACK,UAAU;QACd,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YAC5C,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;YAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;SACpC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,OAAe;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,SAAS,+BAA+B,OAAO,EAAE,CAAC,CAAC;IACzF,CAAC;CACJ;AAjdD,4DAidC"}