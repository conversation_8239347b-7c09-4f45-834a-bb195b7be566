# 完全自动化开发环境启动脚本
Write-Host "=== 完全自动化SSH Shell Integration开发环境 ===" -ForegroundColor Green

# 检查当前目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误：请在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "📁 当前目录正确" -ForegroundColor Green

# 步骤1：安装依赖
Write-Host "`n🔧 步骤1：检查并安装依赖..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "正在安装依赖..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} else {
    Write-Host "✅ 依赖已存在，检查是否需要更新..." -ForegroundColor Green
    npm install
}

# 步骤2：创建scripts目录
Write-Host "`n📁 步骤2：确保scripts目录存在..." -ForegroundColor Yellow
if (-not (Test-Path "scripts")) {
    New-Item -ItemType Directory -Path "scripts" -Force
    Write-Host "✅ scripts目录已创建" -ForegroundColor Green
} else {
    Write-Host "✅ scripts目录已存在" -ForegroundColor Green
}

# 步骤3：初始编译
Write-Host "`n🔨 步骤3：初始编译..." -ForegroundColor Yellow
npm run compile
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 编译失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}
Write-Host "✅ 初始编译成功" -ForegroundColor Green

# 步骤4：启动完全自动化环境
Write-Host "`n🚀 步骤4：启动完全自动化开发环境..." -ForegroundColor Yellow

Write-Host "`n📋 自动化功能说明：" -ForegroundColor Cyan
Write-Host "• TypeScript文件监听和自动编译" -ForegroundColor Gray
Write-Host "• 编译输出监听和自动重载" -ForegroundColor Gray
Write-Host "• 智能防抖动（避免频繁重载）" -ForegroundColor Gray
Write-Host "• 多种重载方式自动尝试" -ForegroundColor Gray
Write-Host "• 详细的状态日志输出" -ForegroundColor Gray

Write-Host "`n🎯 现在将启动以下组件：" -ForegroundColor Cyan
Write-Host "1. TypeScript Watch模式（自动编译）" -ForegroundColor White
Write-Host "2. 自动重载监听器（自动重载扩展）" -ForegroundColor White
Write-Host "3. VSCode扩展调试环境" -ForegroundColor White

$continue = Read-Host "`n是否继续启动完全自动化环境？(y/n)"
if ($continue -ne 'y' -and $continue -ne 'Y') {
    Write-Host "启动已取消" -ForegroundColor Yellow
    exit 0
}

# 启动TypeScript Watch模式
Write-Host "`n👀 启动TypeScript Watch模式..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; npm run watch; Write-Host 'TypeScript Watch模式运行中...' -ForegroundColor Green"

Start-Sleep -Seconds 2

# 启动自动重载监听器
Write-Host "`n🔄 启动自动重载监听器..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; node scripts/vscode-auto-reload.js; Write-Host '自动重载监听器运行中...' -ForegroundColor Green"

Start-Sleep -Seconds 2

# 步骤5：启动说明
Write-Host "`n🎉 完全自动化开发环境已启动！" -ForegroundColor Green

Write-Host "`n📋 现在你需要做的：" -ForegroundColor Cyan
Write-Host "1. 在VSCode中选择调试配置：'Auto Reload Extension'" -ForegroundColor White
Write-Host "2. 按F5启动调试" -ForegroundColor White
Write-Host "3. 等待扩展开发主机窗口打开" -ForegroundColor White
Write-Host "4. 开始修改代码，享受完全自动化的开发体验！" -ForegroundColor White

Write-Host "`n🔄 自动化工作流程：" -ForegroundColor Cyan
Write-Host "修改代码 → 自动编译 → 自动重载 → 立即看到效果" -ForegroundColor Gray

Write-Host "`n📊 监控窗口：" -ForegroundColor Cyan
Write-Host "• TypeScript编译：第一个PowerShell窗口" -ForegroundColor Gray
Write-Host "• 自动重载状态：第二个PowerShell窗口" -ForegroundColor Gray
Write-Host "• 扩展运行日志：VSCode的'SSH Demo Debug'输出通道" -ForegroundColor Gray

Write-Host "`n💡 开发技巧：" -ForegroundColor Yellow
Write-Host "• 保持所有监控窗口打开以观察状态" -ForegroundColor Gray
Write-Host "• 修改代码后等待3-5秒自动重载完成" -ForegroundColor Gray
Write-Host "• 如果自动重载失败，会有提示信息" -ForegroundColor Gray
Write-Host "• 可以随时关闭监控窗口停止自动化" -ForegroundColor Gray

Write-Host "`n🛠️ 故障排除：" -ForegroundColor Yellow
Write-Host "• 如果自动重载不工作，检查监控窗口的错误信息" -ForegroundColor Gray
Write-Host "• 编译错误会阻止自动重载，先修复编译错误" -ForegroundColor Gray
Write-Host "• 可以手动按Ctrl+R作为备用重载方式" -ForegroundColor Gray

Write-Host "`n✅ 环境准备完成！现在可以开始完全自动化的开发了！" -ForegroundColor Green
Write-Host "🚀 在VSCode中选择'Auto Reload Extension'配置并按F5启动调试" -ForegroundColor White

Read-Host "`n按任意键退出"
