{"name": "vscode-extension-basic", "displayName": "Basic VSCode Extension", "description": "A basic VSCode extension template", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:extension.helloWorld"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "extension.helloWorld", "title": "Hello World"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "dev": "tsc -watch -p ./", "auto-dev": "concurrently \"npm run watch\" \"npm run watch-reload\"", "watch-reload": "nodemon --watch out --ext js --exec \"echo Extension files changed, please reload VSCode extension host\"", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/node": "^16.18.126", "@types/vscode": "^1.74.0", "chokidar": "^3.5.3", "concurrently": "^8.2.2", "nodemon": "^3.0.2", "typescript": "^4.9.4"}}