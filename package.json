{"name": "ssh-shell-integration-demo", "displayName": "SSH Shell Integration Demo", "description": "A demo extension showcasing VSCode Shell Integration with SSH connections", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:ssh-demo.connectSSH"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ssh-demo.connectSSH", "title": "SSH Demo: Connect and Execute Commands"}, {"command": "ssh-demo.showDebugInfo", "title": "SSH Demo: Show Debug Information"}], "configuration": {"title": "SSH Demo Configuration", "properties": {"sshDemo.defaultHost": {"type": "string", "default": "***********", "description": "Default SSH host to connect to"}, "sshDemo.defaultUser": {"type": "string", "default": "root", "description": "Default SSH username"}, "sshDemo.commandTimeout": {"type": "number", "default": 30000, "description": "Command execution timeout in milliseconds"}, "sshDemo.enableDebugLogging": {"type": "boolean", "default": true, "description": "Enable detailed debug logging"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "dev": "tsc -watch -p ./", "auto-dev": "concurrently \"npm run watch\" \"npm run watch-reload\"", "watch-reload": "nodemon --watch out --ext js --exec \"echo Extension files changed, please reload VSCode extension host\"", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4", "nodemon": "^3.0.2", "concurrently": "^8.2.2", "chokidar": "^3.5.3"}}