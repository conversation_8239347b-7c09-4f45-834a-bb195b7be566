{"name": "ssh-shell-integration-demo", "displayName": "SSH Shell Integration Demo", "description": "A demo extension showcasing VSCode Shell Integration with SSH connections", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onCommand:ssh-demo.connectSSH"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ssh-demo.connectSSH", "title": "SSH Demo: Connect and Execute Commands"}, {"command": "ssh-demo.showDebugInfo", "title": "SSH Demo: Show Debug Information"}], "configuration": {"title": "SSH Demo Configuration", "properties": {"sshDemo.defaultHost": {"type": "string", "default": "***********", "description": "Default SSH host to connect to"}, "sshDemo.defaultUser": {"type": "string", "default": "root", "description": "Default SSH username"}, "sshDemo.commandTimeout": {"type": "number", "default": 30000, "description": "Command execution timeout in milliseconds"}, "sshDemo.enableDebugLogging": {"type": "boolean", "default": true, "description": "Enable detailed debug logging"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}