{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "npm: compile", "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**"]}, {"name": "Run Extension (Watch Mode)", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "Watch TypeScript", "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**"]}]}