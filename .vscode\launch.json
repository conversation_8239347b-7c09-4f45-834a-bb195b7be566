{"version": "0.2.0", "configurations": [{"name": "🚀 F5 Auto Development", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "F5 Auto Setup", "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**"], "autoAttachChildProcesses": true}, {"name": "Run Extension (Basic)", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "npm: compile", "sourceMaps": true, "smartStep": true, "skipFiles": ["<node_internals>/**"]}]}