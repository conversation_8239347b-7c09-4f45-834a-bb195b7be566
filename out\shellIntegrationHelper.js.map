{"version": 3, "file": "shellIntegrationHelper.js", "sourceRoot": "", "sources": ["../src/shellIntegrationHelper.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC;;;GAGG;AACH,MAAa,sBAAsB;IAQ/B,YAAY,aAAmC;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IACrG,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,IAAY;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;QAC3E,IAAI,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE;YAChC,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3D;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,IAAY;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;QAC7E,IAAI,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE;YAChC,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3D;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,IAAY;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE;YAChC,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC3D;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,4BAA4B,CAAC,IAAY;QAC5C,OAAO,IAAI;aACN,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;aAC3F,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;aACzF,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACxG,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,IAAY;QAClC,OAAO,IAAI;aACN,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;aACzB,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;aACzB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,IAAY;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,OAAe;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,SAAS,uBAAuB,OAAO,EAAE,CAAC,CAAC;SAChF;IACL,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,MAAc;QAOvC,MAAM,QAAQ,GAAG;YACb,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YAClD,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC9C,iBAAiB,EAAE,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC;YACxD,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC;YACtD,SAAS,EAAE,MAAM;SACpB,CAAC;QAEF,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC;gBAC/B,cAAc,EAAE,QAAQ,CAAC,cAAc;gBACvC,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;gBAC7C,YAAY,EAAE,MAAM,CAAC,MAAM;gBAC3B,iBAAiB,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM;aACjD,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;SAClB;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;;AAjHL,wDAkHC;AAjH2B,2CAAoB,GAAG,gBAAgB,CAAC;AACxC,yCAAkB,GAAG,gBAAgB,CAAC;AACtC,8CAAuB,GAAG,gBAAgB,CAAC"}