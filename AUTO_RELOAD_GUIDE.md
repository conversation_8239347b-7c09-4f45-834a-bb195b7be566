# 🔄 自动重载开发指南

## 🚀 快速开始

### 方法1：使用开发脚本（推荐）
```powershell
.\start-dev-mode.ps1
```

### 方法2：手动启动
```bash
# 启动TypeScript watch模式
npm run watch

# 然后在VSCode中选择"Run Extension (Watch Mode)"并按F5
```

## 🔧 自动重载机制

### 1. TypeScript Watch模式
- **功能**：监听`.ts`文件变化，自动编译到`out/`目录
- **启动**：`npm run watch`
- **状态**：在终端中显示编译状态和错误

### 2. VSCode扩展重载
- **快捷键**：在扩展开发主机窗口按 `Ctrl+R`
- **功能**：重新加载扩展而不重启整个调试会话
- **优势**：保持调试状态，快速测试修改

### 3. 调试配置
- **普通模式**：`Run Extension` - 单次编译和启动
- **开发模式**：`Run Extension (Watch Mode)` - 配合watch模式使用

## 📋 开发工作流程

### 标准开发流程
1. **启动开发环境**
   ```bash
   .\start-dev-mode.ps1
   ```

2. **选择调试配置**
   - 在VSCode调试面板选择 `Run Extension (Watch Mode)`
   - 按 `F5` 启动

3. **修改代码**
   - 编辑 `src/` 目录下的任何 `.ts` 文件
   - 保存文件

4. **自动编译**
   - Watch模式自动检测文件变化
   - 自动编译到 `out/` 目录
   - 在watch窗口中显示编译结果

5. **重载扩展**
   - 在扩展开发主机窗口按 `Ctrl+R`
   - 扩展立即重新加载

6. **测试功能**
   - 按 `Ctrl+Shift+P` 测试命令
   - 查看输出通道的日志
   - 验证修改效果

### 快速修改测试循环
```
修改代码 → 保存 → 自动编译 → Ctrl+R → 测试 → 重复
```

## 🛠️ 配置详解

### package.json scripts
```json
{
  "scripts": {
    "compile": "tsc -p ./",           // 单次编译
    "watch": "tsc -watch -p ./",      // 监听模式
    "dev": "tsc -watch -p ./"         // 开发模式别名
  }
}
```

### .vscode/tasks.json
```json
{
  "label": "Watch TypeScript",
  "type": "npm",
  "script": "watch",
  "isBackground": true,              // 后台运行
  "problemMatcher": ["$tsc-watch"],  // 错误检测
  "runOptions": {
    "runOn": "folderOpen"            // 打开文件夹时自动运行
  }
}
```

### .vscode/launch.json
```json
{
  "name": "Run Extension (Watch Mode)",
  "preLaunchTask": "Watch TypeScript",  // 启动前运行watch任务
  "sourceMaps": true,                   // 启用源码映射
  "smartStep": true,                    // 智能步进
  "skipFiles": ["<node_internals>/**"] // 跳过Node.js内部文件
}
```

## 📊 监控和调试

### 1. TypeScript编译监控
- **位置**：Watch模式终端窗口
- **信息**：编译状态、错误、警告
- **示例**：
  ```
  [上午10:30:15] File change detected. Starting incremental compilation...
  [上午10:30:16] Found 0 errors. Watching for file changes.
  ```

### 2. 扩展运行日志
- **位置**：扩展开发主机的"SSH Demo Debug"输出通道
- **内容**：插件运行日志、调试信息
- **用途**：验证功能是否正常工作

### 3. 调试控制台
- **位置**：原VSCode窗口的调试控制台
- **内容**：JavaScript运行时错误、console.log输出
- **快捷键**：`Ctrl+Shift+Y`

## 💡 开发技巧

### 1. 高效的修改测试
```typescript
// 在代码中添加临时日志
console.log('调试信息:', data);
outputChannel.appendLine('测试输出: ' + message);

// 修改后按Ctrl+R立即测试
```

### 2. 断点调试
- 在源码中设置断点
- 修改代码后按 `Ctrl+R` 重载
- 断点在新代码中仍然有效

### 3. 快速错误定位
- 观察watch窗口的编译错误
- 使用VSCode的问题面板（`Ctrl+Shift+M`）
- 检查调试控制台的运行时错误

### 4. 配置热重载
```json
// 在.vscode/settings.json中添加
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/out/**": false  // 不排除out目录，以便监控编译输出
  }
}
```

## 🔍 故障排除

### 问题1：Watch模式不工作
**解决方案**：
```bash
# 停止现有的watch进程
Ctrl+C

# 重新启动
npm run watch
```

### 问题2：修改后扩展没有更新
**检查步骤**：
1. 确认文件已保存
2. 检查watch窗口是否有编译错误
3. 确认按了 `Ctrl+R` 重载扩展
4. 检查是否在正确的窗口中重载

### 问题3：编译错误
**解决方案**：
1. 查看watch窗口的详细错误信息
2. 修复TypeScript错误
3. 保存文件，自动重新编译

### 问题4：断点不命中
**解决方案**：
1. 确认 `sourceMaps: true` 已配置
2. 检查 `.js.map` 文件是否生成
3. 重新启动调试会话

## 📈 性能优化

### 1. 编译性能
```json
// tsconfig.json优化
{
  "compilerOptions": {
    "incremental": true,        // 增量编译
    "tsBuildInfoFile": ".tsbuildinfo"
  }
}
```

### 2. 文件监听优化
```json
// .vscode/settings.json
{
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.git/**": true,
    "**/out/**": false
  }
}
```

## 🎯 最佳实践

1. **保持watch模式运行**：开发期间始终保持TypeScript watch模式运行
2. **使用Ctrl+R重载**：避免重启整个调试会话
3. **监控编译输出**：及时发现和修复编译错误
4. **合理使用日志**：在关键位置添加调试日志
5. **定期清理**：删除临时调试代码

现在你可以享受高效的自动重载开发体验了！🚀
