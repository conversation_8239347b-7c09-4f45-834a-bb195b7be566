const chokidar = require('chokidar');
const fs = require('fs');
const path = require('path');

/**
 * 智能重载器 - 不发送按键，只提供智能提示
 * 避免影响主编辑器窗口
 */
class SmartReloader {
    constructor() {
        this.isMonitoring = false;
        this.outputBuffer = '';
        this.lastReloadTime = 0;
        this.minReloadInterval = 3000;
        this.changeCount = 0;
        
        console.log('🧠 智能重载器启动...');
        console.log('💡 检测到变化时会提供重载提示，不会自动发送按键');
        this.setupWatcher();
        this.setupCleanup();
    }

    setupWatcher() {
        // 监听out目录下的js文件变化
        const watcher = chokidar.watch('./out/**/*.js', {
            ignored: /node_modules/,
            persistent: true,
            ignoreInitial: true,
            awaitWriteFinish: {
                stabilityThreshold: 1000,
                pollInterval: 100
            }
        });

        watcher
            .on('add', (filePath) => {
                console.log(`📁 新文件: ${path.relative(process.cwd(), filePath)}`);
                this.handleChange('add', filePath);
            })
            .on('change', (filePath) => {
                console.log(`📝 文件更新: ${path.relative(process.cwd(), filePath)}`);
                this.handleChange('change', filePath);
            })
            .on('unlink', (filePath) => {
                console.log(`🗑️ 文件删除: ${path.relative(process.cwd(), filePath)}`);
                this.handleChange('unlink', filePath);
            })
            .on('error', (error) => {
                console.error(`❌ 监听错误: ${error}`);
            });

        console.log('👀 正在监听编译输出文件变化...');
        console.log('🔄 检测到变化时会提供重载提示');
    }

    handleChange(type, filePath) {
        const now = Date.now();
        
        // 防抖处理
        if (now - this.lastReloadTime < this.minReloadInterval) {
            return;
        }

        this.lastReloadTime = now;
        this.changeCount++;

        console.log('\n' + '='.repeat(60));
        console.log(`🔄 检测到第 ${this.changeCount} 次代码变化`);
        console.log(`📁 变化类型: ${type}`);
        console.log(`📄 文件路径: ${path.relative(process.cwd(), filePath)}`);
        console.log(`⏰ 时间: ${new Date().toLocaleTimeString()}`);
        
        this.showReloadInstructions();
        console.log('='.repeat(60) + '\n');
    }

    showReloadInstructions() {
        console.log('\n💡 扩展代码已更新，请选择重载方式:');
        console.log('');
        console.log('🎯 推荐方式:');
        console.log('   1. 在扩展开发主机窗口按 Ctrl+R');
        console.log('   2. 或者重新启动调试会话 (Ctrl+Shift+F5)');
        console.log('');
        console.log('⚠️  注意事项:');
        console.log('   • 请确保在扩展开发主机窗口中按 Ctrl+R');
        console.log('   • 不要在主编辑器窗口按 Ctrl+R (会重载整个窗口)');
        console.log('   • 如果不确定，建议重新启动调试会话');
        console.log('');
        console.log('📊 状态信息:');
        console.log(`   • 监听状态: ✅ 活跃`);
        console.log(`   • 变化次数: ${this.changeCount}`);
        console.log(`   • 最后更新: ${new Date().toLocaleTimeString()}`);
    }

    setupCleanup() {
        const cleanup = () => {
            console.log('\n👋 智能重载器正在关闭...');
            process.exit(0);
        };

        process.on('SIGINT', cleanup);
        process.on('SIGTERM', cleanup);
        process.on('exit', cleanup);
    }

    getDebugInfo() {
        return {
            isMonitoring: this.isMonitoring,
            changeCount: this.changeCount,
            lastReloadTime: this.lastReloadTime,
            timestamp: new Date().toISOString()
        };
    }
}

// 检查是否是通过F5启动的
const isF5Launch = process.argv.includes('--f5-launch') || process.env.VSCODE_F5_LAUNCH;

if (isF5Launch) {
    console.log('🚀 F5智能重载模式启动...');
    console.log('✅ TypeScript监听和智能重载提示已激活');
    console.log('💡 修改代码后会收到重载提示，不会自动发送按键');
} else {
    console.log('🎯 启动智能重载监听器...');
    console.log('📋 功能说明:');
    console.log('   • 监听 out/**/*.js 文件变化');
    console.log('   • 提供智能重载提示');
    console.log('   • 不会自动发送按键 (避免影响主编辑器)');
    console.log('   • 防止频繁提示 (最小间隔3秒)');
    console.log('   • 支持优雅退出 (Ctrl+C)');
}
console.log('');

new SmartReloader();
