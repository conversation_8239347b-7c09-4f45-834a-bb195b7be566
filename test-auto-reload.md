# 🧪 自动重载测试指南

## 🎯 测试自动重载功能

### 步骤1：启动开发模式
```powershell
.\start-dev-mode.ps1
```

### 步骤2：启动调试
1. 在VSCode调试面板选择 `Run Extension (Watch Mode)`
2. 按 `F5` 启动调试
3. 等待扩展开发主机窗口打开

### 步骤3：测试基本功能
1. 在扩展开发主机窗口按 `Ctrl+Shift+P`
2. 输入 "SSH Demo: Show Debug Information"
3. 确认插件正常工作

## 🔄 测试自动重载

### 测试1：修改日志消息
1. **修改文件**：打开 `src/extension.ts`
2. **找到行**：`outputChannel.appendLine('=== SSH Shell Integration Demo 启动 ===');`
3. **修改为**：`outputChannel.appendLine('=== SSH Shell Integration Demo 启动 (已修改) ===');`
4. **保存文件**：`Ctrl+S`
5. **观察**：Watch窗口显示编译完成
6. **重载扩展**：在扩展开发主机窗口按 `Ctrl+R`
7. **验证**：查看输出通道，应该显示新的消息

### 测试2：添加新的调试信息
1. **修改文件**：打开 `src/sshTerminalManager.ts`
2. **在connectSSH方法开始处添加**：
   ```typescript
   this.log(`[自动重载测试] 连接参数: ${username}@${host}`);
   ```
3. **保存文件**
4. **重载扩展**：`Ctrl+R`
5. **测试**：执行SSH连接命令，查看新的日志

### 测试3：修改用户界面
1. **修改文件**：打开 `src/extension.ts`
2. **找到**：`'SSH Demo: Connect and Execute Commands'`
3. **修改为**：`'SSH Demo: Connect and Execute Commands (开发版)'`
4. **保存并重载**
5. **验证**：命令面板中应显示新的命令名称

### 测试4：添加新功能
1. **修改文件**：打开 `src/extension.ts`
2. **在activate函数中添加新命令**：
   ```typescript
   const testCommand = vscode.commands.registerCommand('ssh-demo.testAutoReload', () => {
       vscode.window.showInformationMessage('自动重载测试成功！时间: ' + new Date().toLocaleTimeString());
   });
   context.subscriptions.push(testCommand);
   ```
3. **修改package.json**，在commands数组中添加：
   ```json
   {
       "command": "ssh-demo.testAutoReload",
       "title": "SSH Demo: Test Auto Reload"
   }
   ```
4. **保存所有文件**
5. **重载扩展**：`Ctrl+R`
6. **测试**：在命令面板中应该能找到新命令

## 📊 监控重载过程

### Watch窗口输出示例
```
[下午2:30:15] File change detected. Starting incremental compilation...
src/extension.ts(25,5): error TS2304: Cannot find name 'test'.
[下午2:30:16] Found 1 error. Watching for file changes.

# 修复错误后
[下午2:30:45] File change detected. Starting incremental compilation...
[下午2:30:46] Found 0 errors. Watching for file changes.
```

### 扩展重载确认
在扩展开发主机窗口按 `Ctrl+R` 后，应该看到：
- 窗口短暂闪烁
- 输出通道显示新的启动日志
- 命令面板中的命令更新

## 🐛 常见问题和解决方案

### 问题1：修改后没有效果
**检查清单**：
- [ ] 文件已保存
- [ ] Watch窗口显示编译成功
- [ ] 按了 `Ctrl+R` 重载扩展
- [ ] 在正确的窗口中重载

### 问题2：编译错误
**解决步骤**：
1. 查看Watch窗口的错误信息
2. 修复TypeScript错误
3. 保存文件
4. 等待重新编译成功

### 问题3：新命令不出现
**可能原因**：
- package.json中未添加命令定义
- 命令注册代码有错误
- 扩展未正确重载

**解决方案**：
1. 检查package.json的contributes.commands
2. 确认命令注册代码正确
3. 重启整个调试会话

### 问题4：断点失效
**解决方案**：
1. 确认sourceMaps配置正确
2. 重新设置断点
3. 如果仍然失效，重启调试会话

## 🚀 高效开发技巧

### 1. 快速修改循环
```
修改代码 → Ctrl+S → 观察编译 → Ctrl+R → 测试 → 重复
```

### 2. 多文件修改
- 修改多个文件后一起保存
- 等待所有文件编译完成
- 一次性重载扩展

### 3. 使用代码片段
在VSCode中创建自定义代码片段：
```json
{
  "SSH Debug Log": {
    "prefix": "sshlog",
    "body": [
      "this.log('$1: ' + $2);"
    ],
    "description": "SSH调试日志"
  }
}
```

### 4. 快捷键设置
在keybindings.json中添加：
```json
{
  "key": "ctrl+shift+r",
  "command": "workbench.action.reloadWindow",
  "when": "inExtensionDevelopment"
}
```

## 📈 性能监控

### 编译时间
- 初始编译：通常2-5秒
- 增量编译：通常0.5-2秒
- 如果编译时间过长，检查tsconfig.json配置

### 内存使用
- 监控VSCode内存使用
- 长时间开发后可能需要重启VSCode
- 关闭不必要的扩展

## ✅ 测试完成检查

完成所有测试后，确认：
- [ ] 自动编译正常工作
- [ ] 扩展重载功能正常
- [ ] 修改能够立即生效
- [ ] 调试日志正确显示
- [ ] 新功能能够正常添加

现在你已经掌握了高效的自动重载开发流程！🎉
