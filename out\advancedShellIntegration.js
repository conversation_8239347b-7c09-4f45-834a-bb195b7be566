"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedShellIntegration = void 0;
const shellIntegrationHelper_1 = require("./shellIntegrationHelper");
/**
 * 高级Shell Integration实现
 * 提供更真实的终端输出监听和Shell Integration处理
 */
class AdvancedShellIntegration {
    constructor(outputChannel) {
        this.isMonitoring = false;
        this.outputBuffer = '';
        this.lastOutputTime = 0;
        this.outputChannel = outputChannel;
        this.shellHelper = new shellIntegrationHelper_1.ShellIntegrationHelper(outputChannel);
    }
    /**
     * 设置要监听的终端
     */
    setTerminal(terminal) {
        this.terminal = terminal;
        this.log(`设置监听终端: ${terminal.name}`);
    }
    /**
     * 执行命令并监听Shell Integration标记
     */
    async executeCommandWithIntegration(command, timeoutMs = 30000) {
        if (!this.terminal) {
            throw new Error('未设置终端');
        }
        this.log(`开始执行命令: ${command}`);
        this.resetState();
        return new Promise((resolve) => {
            let resolved = false;
            let commandStarted = false;
            let commandCompleted = false;
            let shellIntegrationDetected = false;
            const startTime = Date.now();
            // 超时处理
            const timeoutHandle = setTimeout(() => {
                if (!resolved) {
                    resolved = true;
                    this.stopMonitoring();
                    this.log(`命令执行超时: ${timeoutMs}ms`);
                    resolve({
                        success: false,
                        output: this.outputBuffer,
                        error: '命令执行超时',
                        timedOut: true,
                        shellIntegrationDetected
                    });
                }
            }, timeoutMs);
            // 开始监听终端输出
            this.startMonitoring((output) => {
                if (resolved)
                    return;
                this.outputBuffer += output;
                this.lastOutputTime = Date.now();
                this.log(`收到输出: ${this.shellHelper.escapeControlChars(output)}`);
                // 分析Shell Integration标记
                const analysis = this.shellHelper.analyzeTerminalOutput(output);
                if (analysis.hasStartMarker || analysis.hasExecutedMarker) {
                    shellIntegrationDetected = true;
                    if (!commandStarted) {
                        commandStarted = true;
                        this.log('检测到Shell Integration命令开始标记');
                    }
                }
                if (analysis.hasEndMarker && commandStarted) {
                    commandCompleted = true;
                    shellIntegrationDetected = true;
                    this.log('检测到Shell Integration命令结束标记');
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(timeoutHandle);
                        this.stopMonitoring();
                        const cleanOutput = this.shellHelper.cleanShellIntegrationMarkers(this.outputBuffer);
                        resolve({
                            success: true,
                            output: cleanOutput,
                            timedOut: false,
                            shellIntegrationDetected: true
                        });
                    }
                }
            });
            // 发送命令
            this.terminal.sendText(command);
            this.log(`命令已发送到终端`);
            // 对于SSH等长连接，如果没有Shell Integration标记，使用备用策略
            const fallbackCheck = setInterval(() => {
                if (resolved) {
                    clearInterval(fallbackCheck);
                    return;
                }
                const elapsed = Date.now() - startTime;
                const timeSinceLastOutput = Date.now() - this.lastOutputTime;
                // 如果超过5秒没有Shell Integration标记，且输出稳定超过2秒，认为命令完成
                if (elapsed > 5000 && !shellIntegrationDetected && timeSinceLastOutput > 2000 && this.outputBuffer.length > 0) {
                    this.log('使用备用策略判断命令完成（无Shell Integration标记）');
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(timeoutHandle);
                        clearInterval(fallbackCheck);
                        this.stopMonitoring();
                        resolve({
                            success: true,
                            output: this.outputBuffer,
                            timedOut: false,
                            shellIntegrationDetected: false
                        });
                    }
                }
            }, 500);
        });
    }
    /**
     * 开始监听终端输出
     */
    startMonitoring(onOutput) {
        if (this.isMonitoring) {
            this.stopMonitoring();
        }
        this.isMonitoring = true;
        this.log('开始监听终端输出');
        // 注意：VSCode的Terminal API在某些版本中可能不支持直接监听输出
        // 这里提供一个模拟实现，在实际使用中可能需要其他方法
        this.simulateTerminalOutputMonitoring(onOutput);
    }
    /**
     * 停止监听终端输出
     */
    stopMonitoring() {
        if (this.isMonitoring) {
            this.isMonitoring = false;
            this.log('停止监听终端输出');
        }
    }
    /**
     * 模拟终端输出监听
     * 在实际实现中，你可能需要使用其他方法来获取终端输出
     */
    simulateTerminalOutputMonitoring(onOutput) {
        let simulationStep = 0;
        const simulationInterval = setInterval(() => {
            if (!this.isMonitoring) {
                clearInterval(simulationInterval);
                return;
            }
            // 模拟不同阶段的输出
            let mockOutput = '';
            switch (simulationStep) {
                case 0:
                    // 模拟Shell Integration开始标记
                    mockOutput = '\x1b]633;B\x07';
                    break;
                case 1:
                    mockOutput = 'total 24\n';
                    break;
                case 2:
                    mockOutput = 'drwxr-xr-x  2 <USER> <GROUP> 4096 Dec  1 10:30 .ssh\n';
                    break;
                case 3:
                    mockOutput = '-rw-r--r--  1 <USER> <GROUP>  571 Dec  1 09:15 .bashrc\n';
                    break;
                case 4:
                    mockOutput = '-rw-r--r--  1 <USER> <GROUP>  148 Dec  1 09:15 .profile\n';
                    break;
                case 5:
                    mockOutput = 'drwxr-xr-x  3 <USER> <GROUP> 4096 Dec  1 10:45 documents\n';
                    break;
                case 6:
                    mockOutput = '-rw-------  1 <USER> <GROUP> 1024 Dec  1 11:20 .bash_history\n';
                    break;
                case 7:
                    // 模拟Shell Integration结束标记
                    mockOutput = '\x1b]633;D\x07';
                    clearInterval(simulationInterval);
                    break;
            }
            if (mockOutput) {
                onOutput(mockOutput);
            }
            simulationStep++;
        }, 300);
    }
    /**
     * 重置状态
     */
    resetState() {
        this.outputBuffer = '';
        this.lastOutputTime = Date.now();
        this.stopMonitoring();
    }
    /**
     * 获取当前输出缓冲区
     */
    getOutputBuffer() {
        return this.outputBuffer;
    }
    /**
     * 获取调试信息
     */
    getDebugInfo() {
        return {
            isMonitoring: this.isMonitoring,
            outputBufferLength: this.outputBuffer.length,
            lastOutputTime: this.lastOutputTime,
            hasTerminal: !!this.terminal,
            terminalName: this.terminal?.name
        };
    }
    /**
     * 记录日志
     */
    log(message) {
        const timestamp = new Date().toISOString();
        this.outputChannel.appendLine(`[${timestamp}] AdvancedShellIntegration: ${message}`);
    }
}
exports.AdvancedShellIntegration = AdvancedShellIntegration;
//# sourceMappingURL=advancedShellIntegration.js.map