"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedShellIntegration = void 0;
// 首先在文件顶部添加child_process导入
const vscode = require("vscode");
const shellIntegrationHelper_1 = require("./shellIntegrationHelper");
const child = require("child_process"); // 添加这一行导入child_process
/**
 * 高级Shell Integration实现
 * 提供更真实的终端输出监听和Shell Integration处理
 */
class AdvancedShellIntegration {
    constructor(outputChannel) {
        this.isMonitoring = false;
        this.outputBuffer = '';
        this.lastOutputTime = 0;
        this.outputChannel = outputChannel;
        this.shellHelper = new shellIntegrationHelper_1.ShellIntegrationHelper(outputChannel);
    }
    /**
     * 设置要监听的终端
     */
    setTerminal(terminal) {
        this.terminal = terminal;
        this.log(`设置监听终端: ${terminal.name}`);
    }
    /**
     * 执行命令并监听Shell Integration标记
     */
    async executeCommandWithIntegration(command, timeoutMs = 30000) {
        if (!this.terminal) {
            throw new Error('未设置终端');
        }
        this.log(`开始执行命令: ${command}`);
        this.resetState();
        return new Promise((resolve) => {
            let resolved = false;
            let commandStarted = false;
            let shellIntegrationDetected = false;
            const startTime = Date.now();
            // 超时处理
            const timeoutHandle = setTimeout(() => {
                if (!resolved) {
                    resolved = true;
                    this.stopMonitoring();
                    this.log(`命令执行超时: ${timeoutMs}ms`);
                    resolve({
                        success: false,
                        output: this.outputBuffer,
                        error: '命令执行超时',
                        timedOut: true,
                        shellIntegrationDetected
                    });
                }
            }, timeoutMs);
            // 开始监听终端输出
            this.startMonitoring((output) => {
                if (resolved)
                    return;
                this.outputBuffer += output;
                this.lastOutputTime = Date.now();
                this.log(`收到输出: ${this.shellHelper.escapeControlChars(output)}`);
                // 分析Shell Integration标记
                const analysis = this.shellHelper.analyzeTerminalOutput(output);
                if (analysis.hasStartMarker || analysis.hasExecutedMarker) {
                    shellIntegrationDetected = true;
                    if (!commandStarted) {
                        commandStarted = true;
                        this.log('检测到Shell Integration命令开始标记');
                    }
                }
                if (analysis.hasEndMarker && commandStarted) {
                    shellIntegrationDetected = true;
                    this.log('检测到Shell Integration命令结束标记');
                    if (!resolved) {
                        resolved = true;
                        clearTimeout(timeoutHandle);
                        this.stopMonitoring();
                        const cleanOutput = this.shellHelper.cleanShellIntegrationMarkers(this.outputBuffer);
                        resolve({
                            success: true,
                            output: cleanOutput,
                            timedOut: false,
                            shellIntegrationDetected: true
                        });
                    }
                }
            });
            // 发送命令
            this.terminal.sendText(command);
            this.log(`命令已发送到终端`);
            // 对于SSH等长连接，使用用户交互来确认命令完成
            const fallbackCheck = setInterval(async () => {
                if (resolved) {
                    clearInterval(fallbackCheck);
                    return;
                }
                const elapsed = Date.now() - startTime;
                // 如果超过10秒没有Shell Integration标记，询问用户命令是否完成
                if (elapsed > 10000 && !shellIntegrationDetected) {
                    this.log('未检测到Shell Integration标记，询问用户命令是否完成');
                    const userResponse = await vscode.window.showInformationMessage('未能自动检测到命令完成。请查看终端，命令是否已经执行完成？', '是的，已完成', '否，还在执行', '取消');
                    if (userResponse === '是的，已完成') {
                        if (!resolved) {
                            resolved = true;
                            clearTimeout(timeoutHandle);
                            clearInterval(fallbackCheck);
                            this.stopMonitoring();
                            // 请求用户输入命令输出
                            const userOutput = await vscode.window.showInputBox({
                                prompt: '请复制并粘贴终端中的命令输出（可选）',
                                placeHolder: '如果不需要保存输出，可以直接按回车'
                            });
                            resolve({
                                success: true,
                                output: userOutput || '用户确认命令已完成',
                                timedOut: false,
                                shellIntegrationDetected: false
                            });
                        }
                    }
                    else if (userResponse === '取消') {
                        if (!resolved) {
                            resolved = true;
                            clearTimeout(timeoutHandle);
                            clearInterval(fallbackCheck);
                            this.stopMonitoring();
                            resolve({
                                success: false,
                                output: '',
                                error: '用户取消了命令执行',
                                timedOut: false,
                                shellIntegrationDetected: false
                            });
                        }
                    }
                    // 如果用户选择"否，还在执行"，继续等待
                }
            }, 2000);
        });
    }
    /**
     * 开始监听终端输出
     */
    startMonitoring(onOutput) {
        if (this.isMonitoring) {
            this.stopMonitoring();
        }
        this.isMonitoring = true;
        this.log('开始监听终端输出');
        // 使用真实的终端监听方法
        this.realTerminalOutputMonitoring(onOutput);
    }
    /**
     * 停止监听终端输出
     */
    stopMonitoring() {
        if (this.isMonitoring) {
            this.isMonitoring = false;
            this.log('停止监听终端输出');
        }
    }
    /**
     * 真实的终端输出监听
     * 使用VSCode的实际API来监听终端输出
     */
    realTerminalOutputMonitoring(onOutput) {
        if (!this.terminal) {
            this.log('错误：未设置终端，无法监听输出');
            return;
        }
        this.log('开始真实终端输出监听');
        // 使用child_process直接执行SSH命令并监听其输出
        this.executeSSHCommandWithChildProcess(onOutput);
        // 保留原有的终端关闭事件监听
        const disposable = vscode.window.onDidCloseTerminal(closedTerminal => {
            if (closedTerminal === this.terminal) {
                this.log('监听到终端关闭事件');
                this.stopMonitoring();
                disposable.dispose();
            }
        });
    }
    /**
     * 使用child_process直接执行SSH命令并监听其真实输出
     */
    executeSSHCommandWithChildProcess(onOutput) {
        // 获取当前工作目录
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        // 获取SSH连接信息
        const config = vscode.workspace.getConfiguration('sshDemo');
        const host = config.get('defaultHost', '***********');
        const username = config.get('defaultUser', 'root');
        const password = config.get('defaultPassword', 'InSec@888');
        // 创建一个SSH进程变量
        let sshProcess = null;
        // 使用expect脚本自动处理SSH密码输入（Windows上需要安装expect）
        // 对于Windows，我们可以使用PowerShell或其他方式
        let sshCommand;
        if (process.platform === 'win32') {
            // Windows上使用sshpass（需要安装）或PowerShell脚本
            // 注意：这需要先安装sshpass工具
            // 或者使用PowerShell的SSH模块
            this.log('在Windows上使用PowerShell执行SSH命令');
            // 创建一个临时PowerShell脚本来处理SSH连接
            const tempScriptPath = `${workspaceFolder}\\temp_ssh_script.ps1`;
            const scriptContent = `
$password = "${password}"
$securePassword = ConvertTo-SecureString $password -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential ("${username}", $securePassword)

# 连接到SSH服务器
$session = New-SSHSession -ComputerName ${host} -Credential $credential -AcceptKey

if ($session) {
    Write-Host "SSH连接成功！"
    
    # 执行命令
    $result = Invoke-SSHCommand -SessionId $session.SessionId -Command "ls /root"
    Write-Host $result.Output
    
    # 关闭会话
    Remove-SSHSession -SessionId $session.SessionId
} else {
    Write-Host "SSH连接失败！"
}
`;
            // 将脚本写入临时文件
            const fs = require('fs');
            fs.writeFileSync(tempScriptPath, scriptContent);
            // 执行PowerShell脚本
            sshCommand = `powershell -ExecutionPolicy Bypass -File "${tempScriptPath}"`;
        }
        else {
            // Linux/Mac上使用expect脚本
            this.log('在Linux/Mac上使用expect执行SSH命令');
            // 创建一个临时expect脚本
            const tempScriptPath = `${workspaceFolder}/temp_ssh_script.exp`;
            const scriptContent = `#!/usr/bin/expect -f

set timeout 30
spawn ssh ${username}@${host}

expect {
    "(yes/no)" { send "yes\r"; exp_continue }
    "password:" { send "${password}\r" }
    timeout { exit 1 }
}

expect {
    "$" { send "ls /root\r" }
    "#" { send "ls /root\r" }
    timeout { exit 1 }
}

expect {
    "$" { send "exit\r" }
    "#" { send "exit\r" }
    timeout { exit 1 }
}

expect eof
`;
            // 将脚本写入临时文件并设置执行权限
            const fs = require('fs');
            fs.writeFileSync(tempScriptPath, scriptContent);
            fs.chmodSync(tempScriptPath, '755');
            // 执行expect脚本
            sshCommand = tempScriptPath;
        }
        this.log(`执行SSH命令: ${sshCommand}`);
        try {
            // 执行SSH命令
            sshProcess = child.spawn(sshCommand, [], {
                cwd: workspaceFolder,
                shell: true
            });
            // 监听标准输出
            sshProcess.stdout.on('data', (data) => {
                const outputData = data.toString();
                this.log(`SSH输出: ${outputData.trim()}`);
                // 使用正则表达式检测SSH连接状态
                const loginSuccessPattern = /(Welcome|Login successful|Last login|\$|#|>)/i;
                const passwordPromptPattern = /(password|密码)/i;
                const errorPattern = /(Connection refused|Permission denied|Authentication failed)/i;
                if (loginSuccessPattern.test(outputData)) {
                    this.log('检测到SSH登录成功');
                    onOutput('SSH连接成功');
                }
                else if (passwordPromptPattern.test(outputData)) {
                    this.log('检测到密码提示');
                    onOutput('需要输入密码');
                    // 自动发送密码
                    if (sshProcess.stdin) {
                        sshProcess.stdin.write(`${password}\n`);
                        this.log('已自动发送密码');
                    }
                }
                else if (errorPattern.test(outputData)) {
                    this.log('检测到SSH连接错误');
                    onOutput(`SSH连接错误: ${outputData}`);
                }
                // 将所有输出传递给回调函数
                onOutput(outputData);
            });
            // 监听标准错误
            sshProcess.stderr.on('data', (data) => {
                const errorData = data.toString();
                this.log(`SSH错误: ${errorData.trim()}`);
                onOutput(`错误: ${errorData}`);
            });
            // 监听进程结束
            sshProcess.on('close', (code) => {
                this.log(`SSH进程结束，退出码: ${code}`);
                // 清理临时文件
                try {
                    const fs = require('fs');
                    const tempScriptPath = process.platform === 'win32'
                        ? `${workspaceFolder}\\temp_ssh_script.ps1`
                        : `${workspaceFolder}/temp_ssh_script.exp`;
                    if (fs.existsSync(tempScriptPath)) {
                        fs.unlinkSync(tempScriptPath);
                        this.log(`已删除临时脚本: ${tempScriptPath}`);
                    }
                }
                catch (error) {
                    this.log(`删除临时脚本失败: ${error}`);
                }
            });
            // 处理进程错误
            sshProcess.on('error', (error) => {
                this.log(`SSH进程错误: ${error.message}`);
                onOutput(`进程错误: ${error.message}`);
            });
        }
        catch (error) {
            this.log(`创建SSH进程失败: ${error}`);
        }
    }
    /**
     * 检查命令完成状态
     * 由于无法直接监听输出，使用其他方法判断命令是否完成
     */
    checkCommandCompletion(onOutput) {
        // 这里我们采用一个实用的方法：
        // 1. 假设命令在一定时间后完成
        // 2. 通过用户交互来确认命令完成
        const elapsed = Date.now() - this.lastOutputTime;
        // 如果超过3秒没有新的检查，假设有新输出
        if (elapsed > 3000) {
            this.lastOutputTime = Date.now();
            // 模拟检测到输出（实际应用中这里需要真实的输出获取方法）
            const simulatedOutput = this.generateRealisticOutput();
            if (simulatedOutput) {
                onOutput(simulatedOutput);
            }
        }
    }
    /**
     * 生成真实的输出（基于实际命令执行）
     */
    generateRealisticOutput() {
        // 这里返回空字符串，表示我们依赖用户在终端中看到实际输出
        // 真实的实现需要使用其他方法来获取终端输出
        this.log('注意：当前无法直接获取终端输出，请在终端中查看实际结果');
        return '';
    }
    /**
     * 重置状态
     */
    resetState() {
        this.outputBuffer = '';
        this.lastOutputTime = Date.now();
        this.stopMonitoring();
    }
    /**
     * 获取当前输出缓冲区
     */
    getOutputBuffer() {
        return this.outputBuffer;
    }
    /**
     * 获取调试信息
     */
    getDebugInfo() {
        return {
            isMonitoring: this.isMonitoring,
            outputBufferLength: this.outputBuffer.length,
            lastOutputTime: this.lastOutputTime,
            hasTerminal: !!this.terminal,
            terminalName: this.terminal?.name
        };
    }
    /**
     * 记录日志
     */
    log(message) {
        const timestamp = new Date().toISOString();
        this.outputChannel.appendLine(`[${timestamp}] AdvancedShellIntegration: ${message}`);
    }
}
exports.AdvancedShellIntegration = AdvancedShellIntegration;
//# sourceMappingURL=advancedShellIntegration.js.map