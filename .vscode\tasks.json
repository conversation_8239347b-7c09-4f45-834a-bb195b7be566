{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}, {"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Clean Build", "type": "shell", "command": "<PERSON><PERSON><PERSON>", "args": ["out"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "Auto Development Mode", "type": "shell", "command": "node", "args": ["scripts/auto-reload.js"], "group": "build", "presentation": {"echo": false, "reveal": "silent", "focus": false, "panel": "dedicated", "showReuseMessage": false, "clear": true}, "isBackground": true, "problemMatcher": []}, {"label": "Start Auto Dev Environment", "dependsOrder": "parallel", "dependsOn": ["Watch TypeScript", "Auto Development Mode"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true, "problemMatcher": []}, {"label": "F5 Auto Setup", "type": "shell", "command": "node", "args": ["scripts/f5-auto-setup.js"], "dependsOrder": "sequence", "dependsOn": ["npm: compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "isBackground": true, "problemMatcher": []}]}