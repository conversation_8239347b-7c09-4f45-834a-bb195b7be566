{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"echo": true, "reveal": "never", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}, {"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Clean Build", "type": "shell", "command": "<PERSON><PERSON><PERSON>", "args": ["out"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}]}