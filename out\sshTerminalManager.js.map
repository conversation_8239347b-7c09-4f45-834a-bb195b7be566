{"version": 3, "file": "sshTerminalManager.js", "sourceRoot": "", "sources": ["../src/sshTerminalManager.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yEAAsE;AACtE,qEAAkE;AAElE;;;GAGG;AACH,MAAa,kBAAkB;IAQ3B,YAAY,aAAmC;QAHvC,gBAAW,GAAY,KAAK,CAAC;QAIjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,+CAAsB,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,CAAC,wBAAwB,GAAG,IAAI,mDAAwB,CAAC,aAAa,CAAC,CAAC;QAC5E,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CAAC,IAAY,EAAE,QAAgB;QAClD,IAAI;YACA,IAAI,CAAC,GAAG,CAAC,YAAY,QAAQ,IAAI,IAAI,EAAE,CAAC,CAAC;YAEzC,SAAS;YACT,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;gBACzC,IAAI,EAAE,cAAc,IAAI,EAAE;gBAC1B,SAAS,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW;gBACxE,SAAS,EAAE,EAAE;gBACb,GAAG,EAAE;oBACD,sBAAsB;oBACtB,0BAA0B,EAAE,GAAG;iBAClC;gBACD,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;aAC3C,CAAC,CAAC;YAEH,OAAO;YACP,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAErB,0BAA0B;YAC1B,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEzD,UAAU;YACV,MAAM,UAAU,GAAG,OAAO,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC7C,IAAI,CAAC,GAAG,CAAC,YAAY,UAAU,EAAE,CAAC,CAAC;YAEnC,UAAU;YACV,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEnC,wBAAwB;YACxB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE9B,0BAA0B;YAC1B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC9C,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,iBAAiB;aACjC,CAAC,IAAI,WAAW,CAAC;YAElB,OAAO;YACP,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YAEnC,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC;YAC9B,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,OAAe;QAMvC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC,MAAM,KAAK,GAAG,UAAU,CAAC;YACzB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;SACjE;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE,CAAC,CAAC;QAE7B,IAAI;YACA,4BAA4B;YAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,6BAA6B,CAC5E,OAAO,EACP,IAAI,CAAC,cAAc,CACtB,CAAC;YAEF,IAAI,CAAC,GAAG,CAAC,cAAc,MAAM,CAAC,OAAO,QAAQ,MAAM,CAAC,QAAQ,uBAAuB,MAAM,CAAC,wBAAwB,EAAE,CAAC,CAAC;YAEtH,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC5B,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,QAAQ,GAAG,WAAW,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACnB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;SAC3E;IACL,CAAC;IAED;;OAEG;IACI,UAAU;QACb,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;YAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC5B;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,YAAY;QACf,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,CAAC;QAEvE,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;YAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,wBAAwB,EAAE,iBAAiB;SAC9C,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB;QAC1B,oBAAoB;QACpB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBACnC,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,mBAAmB;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACpB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,GAAG,CAAC,OAAe;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,SAAS,iBAAiB,OAAO,EAAE,CAAC,CAAC;IAC3E,CAAC;CACJ;AA3KD,gDA2KC"}