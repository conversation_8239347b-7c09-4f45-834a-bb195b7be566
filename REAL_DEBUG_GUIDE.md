# 🔧 真实SSH调试指南

## 📋 更新说明

我已经删除了所有模拟数据，现在插件使用真实的SSH连接和终端交互。

### 🔄 主要变化

1. **真实SSH连接**：不再模拟SSH连接，使用实际的ssh命令
2. **用户交互确认**：通过对话框确认连接状态和命令完成
3. **真实终端输出**：依赖用户在终端中查看实际输出
4. **Shell Integration检测**：仍然检测VSCode的Shell Integration标记

## 🚀 使用流程

### 步骤1：启动调试
```bash
npm run compile
```
然后在VSCode中按 `F5`，选择 "Debug Anyway"

### 步骤2：测试SSH连接
1. 在扩展开发主机窗口中按 `Ctrl+Shift+P`
2. 输入 "SSH Demo: Connect and Execute Commands"
3. 输入真实的SSH服务器信息：
   - **主机地址**：你的SSH服务器IP（如 *************）
   - **用户名**：真实的SSH用户名（如 root）

### 步骤3：建立SSH连接
1. 插件会创建新终端并发送SSH命令
2. **在终端中手动输入密码**
3. 等待SSH连接建立
4. 点击弹出对话框中的 **"已连接"** 按钮

### 步骤4：执行命令
1. 插件会在SSH会话中执行 `ls /root` 命令
2. 观察终端中的实际输出
3. 等待10秒后会弹出确认对话框
4. 选择 **"是的，已完成"** 并可选择性地复制输出

## 🔍 调试要点

### 1. 真实SSH服务器
你需要一个真实的SSH服务器来测试：
- Linux服务器（Ubuntu、CentOS等）
- 云服务器（阿里云、腾讯云等）
- 本地虚拟机
- WSL2（Windows Subsystem for Linux）

### 2. SSH配置
确保SSH服务器：
- SSH服务已启动（端口22）
- 允许密码认证
- 防火墙允许SSH连接
- 用户有相应权限

### 3. 网络连接
- 确保网络可达性
- 检查防火墙设置
- 验证SSH端口开放

## 📊 调试信息

### 输出通道日志
在 "SSH Demo Debug" 通道中你会看到：

```
=== SSH Shell Integration Demo 启动 ===
[时间戳] SSHManager: 开始连接SSH: root@*************
[时间戳] AdvancedShellIntegration: 设置监听终端: SSH Demo - *************
[时间戳] SSHManager: 执行SSH命令: ssh root@*************
[时间戳] SSHManager: SSH命令已发送，等待用户在终端中输入密码
[时间戳] SSHManager: SSH连接流程已启动，请在终端中手动输入密码
[时间戳] SSHManager: 执行命令: ls /root
[时间戳] AdvancedShellIntegration: 开始执行命令: ls /root
[时间戳] AdvancedShellIntegration: 命令已发送到终端
[时间戳] AdvancedShellIntegration: 开始真实终端输出监听
```

### 用户交互流程
1. **SSH连接确认**：
   ```
   SSH命令已发送到终端。请在终端中输入密码建立连接。
   连接成功后点击"已连接"。
   [已连接] [连接失败] [取消]
   ```

2. **命令完成确认**：
   ```
   未能自动检测到命令完成。请查看终端，命令是否已经执行完成？
   [是的，已完成] [否，还在执行] [取消]
   ```

3. **输出收集**（可选）：
   ```
   请复制并粘贴终端中的命令输出（可选）
   [输入框]
   ```

## 🛠️ Shell Integration 检测

### 支持的标记
- **命令开始**：`\x1b]633;B\x07`
- **命令执行**：`\x1b]633;C\x07`
- **命令结束**：`\x1b]633;D\x07`

### 检测逻辑
1. 如果检测到Shell Integration标记，自动判断命令完成
2. 如果10秒内未检测到标记，询问用户确认
3. 支持用户手动输入命令输出

## 🔧 故障排除

### 问题1：SSH连接失败
**检查项**：
- SSH服务器是否运行
- 网络连接是否正常
- 用户名密码是否正确
- 防火墙设置

### 问题2：终端无响应
**解决方案**：
- 检查SSH命令格式
- 确认终端类型（PowerShell/Bash）
- 查看调试日志

### 问题3：命令执行超时
**原因**：
- 命令执行时间过长
- 网络延迟
- SSH会话问题

**解决方案**：
- 增加超时时间配置
- 检查网络连接
- 手动确认命令完成

### 问题4：Shell Integration不工作
**说明**：
- 这是正常的，SSH会话通常不支持Shell Integration
- 插件会自动切换到用户交互模式
- 可以手动确认命令完成状态

## 📝 测试建议

### 1. 本地测试
如果没有远程服务器，可以：
- 使用WSL2：`ssh username@localhost`
- 使用Docker容器
- 使用虚拟机

### 2. 命令测试
除了 `ls /root`，还可以测试：
- `pwd` - 显示当前目录
- `whoami` - 显示当前用户
- `date` - 显示当前时间
- `ls -la` - 详细列表

### 3. 网络测试
测试不同网络环境：
- 本地网络
- 公网连接
- VPN环境

## 🎯 学习目标

通过真实调试，你将学会：
- 真实SSH连接处理
- 用户交互设计
- 终端状态监控
- 错误处理和超时机制
- Shell Integration的实际应用场景

现在你可以使用真实的SSH服务器来测试插件的完整功能！
