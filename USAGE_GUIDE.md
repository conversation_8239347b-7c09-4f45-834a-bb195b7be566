# SSH Shell Integration Demo - 使用指南

## 🚀 快速开始

### 1. 环境准备
确保你已安装：
- Node.js (推荐 16.x 或更高版本)
- VSCode (1.74.0 或更高版本)
- PowerShell (Windows环境)

### 2. 项目初始化

#### 方法一：使用启动脚本（推荐）
```powershell
# 在项目根目录运行
.\start-debug.ps1
```

#### 方法二：手动步骤
```bash
# 安装依赖
npm install

# 编译项目
npm run compile
```

### 3. 启动调试

1. 在VSCode中打开项目
2. 按 `F5` 启动扩展开发主机
3. 新窗口会打开，这就是测试环境

## 🔧 功能使用

### 主要命令

在扩展开发主机窗口中按 `Ctrl+Shift+P`，然后输入：

1. **`SSH Demo: Connect and Execute Commands`**
   - 连接SSH并执行 `ls /root` 命令
   - 演示完整的Shell Integration流程

2. **`SSH Demo: Show Debug Information`**
   - 显示当前插件状态和调试信息

### 配置选项

可以在VSCode设置中修改：

```json
{
  "sshDemo.defaultHost": "你的SSH服务器IP",
  "sshDemo.defaultUser": "用户名",
  "sshDemo.commandTimeout": 30000,
  "sshDemo.enableDebugLogging": true
}
```

## 📊 Shell Integration 特性

### 1. 标记检测
插件会检测VSCode Shell Integration的标记：

- **命令开始**: `\x1b]633;B\x07`
- **命令执行**: `\x1b]633;C\x07`  
- **命令结束**: `\x1b]633;D\x07`

### 2. 超时处理
- SSH连接超时：3秒
- 命令执行超时：30秒（可配置）
- 智能备用策略：当没有Shell Integration标记时使用时间和输出稳定性判断

### 3. 输出处理
- 自动清理Shell Integration标记
- 区分终端输出和进程输出
- 实时日志记录

## 🐛 调试功能

### 输出通道
所有操作都会记录到 **"SSH Demo Debug"** 输出通道：

```
[时间戳] SSHManager: 尝试连接到 root@***********
[时间戳] AdvancedShellIntegration: 设置监听终端
[时间戳] ShellIntegration: 检测到命令开始标记
[时间戳] AdvancedShellIntegration: 收到输出: total 24...
[时间戳] ShellIntegration: 检测到命令结束标记
```

### 调试信息
执行调试命令可以看到详细状态：

```json
{
  "isConnected": true,
  "hasTerminal": true,
  "terminalName": "SSH Demo - ***********",
  "commandTimeout": 30000,
  "advancedShellIntegration": {
    "isMonitoring": false,
    "outputBufferLength": 256,
    "lastOutputTime": 1704110400000
  }
}
```

## 🔍 代码结构

### 核心组件

1. **`extension.ts`** - 插件入口点
   - 命令注册
   - 用户交互
   - 进度显示

2. **`sshTerminalManager.ts`** - SSH连接管理
   - 终端创建和管理
   - SSH连接建立
   - 命令执行协调

3. **`advancedShellIntegration.ts`** - 高级Shell Integration
   - 终端输出监听
   - Shell Integration标记处理
   - 超时和备用策略

4. **`shellIntegrationHelper.ts`** - Shell Integration辅助工具
   - 标记检测和清理
   - 输出分析
   - 调试信息

### 关键特性

- ✅ **Shell Integration标记检测**
- ✅ **超时保护机制**
- ✅ **详细调试日志**
- ✅ **备用完成策略**
- ✅ **配置化设置**
- ✅ **错误处理**

## 🛠️ 开发扩展

### 添加新功能

1. **支持更多命令**
   ```typescript
   // 在 sshTerminalManager.ts 中添加
   public async executeCustomCommand(command: string): Promise<Result> {
       return this.advancedShellIntegration.executeCommandWithIntegration(command);
   }
   ```

2. **添加新的Shell Integration标记**
   ```typescript
   // 在 shellIntegrationHelper.ts 中添加
   private static readonly CUSTOM_MARKER = '\x1b]633;X\x07';
   ```

3. **扩展配置选项**
   ```json
   // 在 package.json 的 contributes.configuration 中添加
   "sshDemo.newOption": {
       "type": "string",
       "default": "value",
       "description": "新配置选项"
   }
   ```

### 测试新功能

1. 修改代码后运行 `npm run compile`
2. 在调试窗口中重新加载扩展（`Ctrl+R`）
3. 测试新功能
4. 查看调试输出验证行为

## 📝 注意事项

### 限制和已知问题

1. **终端输出监听**
   - 当前使用模拟实现
   - VSCode Terminal API在某些版本中有限制
   - 可能需要根据具体VSCode版本调整

2. **SSH认证**
   - 当前仅支持密码认证
   - 需要手动输入密码
   - 未来可扩展支持密钥认证

3. **平台兼容性**
   - 主要针对Windows PowerShell环境
   - 其他平台可能需要调整Shell配置

### 最佳实践

1. **启用详细日志** - 便于调试和学习
2. **合理设置超时** - 根据网络环境调整
3. **监控输出通道** - 了解插件运行状态
4. **测试不同场景** - SSH连接失败、命令超时等

## 🎯 学习目标

通过这个示例，你将学会：

- VSCode插件开发基础
- Shell Integration API使用
- 终端管理和命令执行
- 异步操作和超时处理
- 调试和日志记录
- 配置管理和用户交互

这个项目为深入学习VSCode Shell Integration提供了完整的实践基础！
