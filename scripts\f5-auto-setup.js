const { spawn } = require('child_process');
const path = require('path');

/**
 * F5自动化设置脚本
 * 在VSCode F5调试启动时自动启动所有必要的后台进程
 */
class F5AutoSetup {
    constructor() {
        this.processes = [];
        this.isShuttingDown = false;

        console.log('🚀 F5自动化环境初始化...');
        console.log('💡 只有按F5时才会启动自动化环境');
        console.log('📋 正在启动：TypeScript监听 + 自动重载');
        this.setupEnvironment();
        this.setupCleanup();
    }

    async setupEnvironment() {
        try {
            // 1. 启动TypeScript Watch模式
            console.log('📝 启动TypeScript监听模式...');
            await this.startTypeScriptWatch();

            // 2. 启动自动重载监听器
            console.log('🔄 启动自动重载监听器...');
            await this.startAutoReloader();

            console.log('✅ F5自动化环境启动完成！');
            console.log('💡 现在可以修改代码，系统将自动编译和重载扩展');
            console.log('📊 监控状态：TypeScript监听 ✓ | 自动重载 ✓');

            // 保持进程运行
            this.keepAlive();

        } catch (error) {
            console.error('❌ F5自动化环境启动失败:', error.message);
            process.exit(1);
        }
    }

    startTypeScriptWatch() {
        return new Promise((resolve, reject) => {
            const tscProcess = spawn('npx', ['tsc', '-w', '-p', '.'], {
                cwd: process.cwd(),
                stdio: ['ignore', 'pipe', 'pipe'],
                shell: true
            });

            let hasStarted = false;

            tscProcess.stdout.on('data', (data) => {
                const output = data.toString();

                // 检查是否已经开始监听
                if (output.includes('Watching for file changes') && !hasStarted) {
                    hasStarted = true;
                    console.log('✅ TypeScript监听模式已启动');
                    resolve();
                }

                // 显示编译状态（简化）
                if (output.includes('Found 0 errors')) {
                    console.log('📝 TypeScript编译完成');
                } else if (output.includes('error TS')) {
                    console.log('⚠️ TypeScript编译错误，请检查代码');
                }
            });

            tscProcess.stderr.on('data', (data) => {
                const error = data.toString();
                if (!hasStarted && error.includes('error')) {
                    reject(new Error(`TypeScript启动失败: ${error}`));
                }
            });

            tscProcess.on('close', (code) => {
                if (code !== 0 && !this.isShuttingDown) {
                    console.log('⚠️ TypeScript监听进程意外退出');
                }
            });

            this.processes.push(tscProcess);

            // 超时处理
            setTimeout(() => {
                if (!hasStarted) {
                    reject(new Error('TypeScript启动超时'));
                }
            }, 10000);
        });
    }

    startAutoReloader() {
        return new Promise((resolve, reject) => {
            const reloaderProcess = spawn('node', ['scripts/vscode-auto-reload.js', '--f5-launch'], {
                cwd: process.cwd(),
                stdio: ['ignore', 'pipe', 'pipe'],
                shell: true,
                env: { ...process.env, VSCODE_F5_LAUNCH: 'true' }
            });

            let hasStarted = false;

            reloaderProcess.stdout.on('data', (data) => {
                const output = data.toString();

                if (output.includes('正在监听编译输出文件变化') && !hasStarted) {
                    hasStarted = true;
                    console.log('✅ 自动重载监听器已启动');
                    resolve();
                }

                // 显示重载状态
                if (output.includes('文件更新:')) {
                    console.log('🔄 检测到文件变化，准备重载...');
                } else if (output.includes('扩展重载完成')) {
                    console.log('✅ 扩展重载完成');
                }
            });

            reloaderProcess.stderr.on('data', (data) => {
                const error = data.toString();
                if (!hasStarted) {
                    reject(new Error(`自动重载器启动失败: ${error}`));
                }
            });

            reloaderProcess.on('close', (code) => {
                if (code !== 0 && !this.isShuttingDown) {
                    console.log('⚠️ 自动重载进程意外退出');
                }
            });

            this.processes.push(reloaderProcess);

            // 超时处理
            setTimeout(() => {
                if (!hasStarted) {
                    reject(new Error('自动重载器启动超时'));
                }
            }, 5000);
        });
    }

    keepAlive() {
        // 保持主进程运行，监听子进程状态
        setInterval(() => {
            if (!this.isShuttingDown) {
                // 检查进程健康状态
                const aliveProcesses = this.processes.filter(p => !p.killed);
                if (aliveProcesses.length < this.processes.length) {
                    console.log('⚠️ 检测到子进程异常，请检查状态');
                }
            }
        }, 30000); // 每30秒检查一次
    }

    setupCleanup() {
        const cleanup = () => {
            if (this.isShuttingDown) return;

            this.isShuttingDown = true;
            console.log('\n👋 正在关闭F5自动化环境...');

            this.processes.forEach((process, index) => {
                if (!process.killed) {
                    console.log(`🔄 关闭进程 ${index + 1}...`);
                    process.kill('SIGTERM');

                    // 强制关闭
                    setTimeout(() => {
                        if (!process.killed) {
                            process.kill('SIGKILL');
                        }
                    }, 3000);
                }
            });

            setTimeout(() => {
                console.log('✅ F5自动化环境已关闭');
                process.exit(0);
            }, 1000);
        };

        process.on('SIGINT', cleanup);
        process.on('SIGTERM', cleanup);
        process.on('exit', cleanup);
    }
}

// 启动F5自动化设置
new F5AutoSetup();
