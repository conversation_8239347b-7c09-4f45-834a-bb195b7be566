const chokidar = require('chokidar');
const { exec } = require('child_process');
const path = require('path');

/**
 * 自动重载脚本
 * 监听编译输出文件变化，自动发送重载命令到VSCode
 */
class AutoReloader {
    constructor() {
        this.isReloading = false;
        this.reloadTimeout = null;
        this.lastReloadTime = 0;
        this.minReloadInterval = 2000; // 最小重载间隔2秒
        
        console.log('🚀 自动重载监听器启动...');
        this.setupWatcher();
    }

    setupWatcher() {
        // 监听out目录下的js文件变化
        const watcher = chokidar.watch('./out/**/*.js', {
            ignored: /node_modules/,
            persistent: true,
            ignoreInitial: true
        });

        watcher
            .on('add', (filePath) => {
                console.log(`📁 文件添加: ${filePath}`);
                this.scheduleReload();
            })
            .on('change', (filePath) => {
                console.log(`📝 文件修改: ${filePath}`);
                this.scheduleReload();
            })
            .on('unlink', (filePath) => {
                console.log(`🗑️ 文件删除: ${filePath}`);
                this.scheduleReload();
            })
            .on('error', (error) => {
                console.error(`❌ 监听错误: ${error}`);
            });

        console.log('👀 正在监听 out/**/*.js 文件变化...');
        console.log('💡 提示: 修改src目录下的TypeScript文件将自动触发重载');
    }

    scheduleReload() {
        const now = Date.now();
        
        // 防止频繁重载
        if (now - this.lastReloadTime < this.minReloadInterval) {
            if (this.reloadTimeout) {
                clearTimeout(this.reloadTimeout);
            }
            
            this.reloadTimeout = setTimeout(() => {
                this.performReload();
            }, this.minReloadInterval - (now - this.lastReloadTime));
            
            return;
        }

        this.performReload();
    }

    performReload() {
        if (this.isReloading) {
            return;
        }

        this.isReloading = true;
        this.lastReloadTime = Date.now();

        console.log('🔄 检测到文件变化，准备重载扩展...');

        // 尝试多种方式重载VSCode扩展
        this.reloadVSCodeExtension()
            .then(() => {
                console.log('✅ 扩展重载完成');
                this.isReloading = false;
            })
            .catch((error) => {
                console.error('❌ 扩展重载失败:', error.message);
                console.log('💡 请手动在扩展开发主机窗口按 Ctrl+R 重载扩展');
                this.isReloading = false;
            });
    }

    async reloadVSCodeExtension() {
        return new Promise((resolve, reject) => {
            // 方法1: 尝试使用VSCode命令行工具
            const commands = [
                // Windows
                'code --command "workbench.action.reloadWindow"',
                // 通用方法 - 发送按键
                'powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'^r\')"'
            ];

            let commandIndex = 0;

            const tryNextCommand = () => {
                if (commandIndex >= commands.length) {
                    reject(new Error('所有重载方法都失败了'));
                    return;
                }

                const command = commands[commandIndex];
                console.log(`🔧 尝试重载方法 ${commandIndex + 1}: ${command}`);

                exec(command, (error, stdout, stderr) => {
                    if (error) {
                        console.log(`⚠️ 方法 ${commandIndex + 1} 失败: ${error.message}`);
                        commandIndex++;
                        setTimeout(tryNextCommand, 500);
                    } else {
                        console.log(`✅ 重载命令执行成功`);
                        resolve();
                    }
                });
            };

            tryNextCommand();
        });
    }
}

// 启动自动重载器
new AutoReloader();

// 优雅退出处理
process.on('SIGINT', () => {
    console.log('\n👋 自动重载监听器正在退出...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 自动重载监听器正在退出...');
    process.exit(0);
});
