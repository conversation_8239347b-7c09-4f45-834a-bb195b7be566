import * as vscode from 'vscode';
import { SSHTerminalManager } from './sshTerminalManager';

let outputChannel: vscode.OutputChannel;
let sshManager: SSHTerminalManager;

/**
 * 插件激活函数
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('SSH Shell Integration Demo 插件已激活');

    // 创建输出通道
    outputChannel = vscode.window.createOutputChannel('SSH Demo Debug');
    outputChannel.show();
    outputChannel.appendLine('=== SSH Shell Integration Demo 启动 ===');

    // 创建SSH管理器
    sshManager = new SSHTerminalManager(outputChannel);

    // 注册命令：连接SSH并执行命令
    const connectCommand = vscode.commands.registerCommand('ssh-demo.connectSSH', async () => {
        try {
            outputChannel.appendLine('\n--- 开始SSH连接和命令执行演示 ---');

            // 获取配置
            const config = vscode.workspace.getConfiguration('sshDemo');
            const defaultHost = config.get<string>('defaultHost', '***********');
            const defaultUser = config.get<string>('defaultUser', 'root');

            // 让用户输入SSH连接信息
            const host = await vscode.window.showInputBox({
                prompt: '请输入SSH主机地址',
                value: defaultHost,
                placeHolder: '例如: ***********'
            });

            if (!host) {
                vscode.window.showWarningMessage('未输入主机地址，操作已取消');
                return;
            }

            const username = await vscode.window.showInputBox({
                prompt: '请输入SSH用户名',
                value: defaultUser,
                placeHolder: '例如: root'
            });

            if (!username) {
                vscode.window.showWarningMessage('未输入用户名，操作已取消');
                return;
            }

            // 显示进度
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'SSH Demo',
                cancellable: false
            }, async (progress) => {
                // 步骤1: 连接SSH
                progress.report({ increment: 0, message: '正在连接SSH...' });
                outputChannel.appendLine(`尝试连接到 ${username}@${host}`);

                const connected = await sshManager.connectSSH(host, username);

                if (!connected) {
                    vscode.window.showErrorMessage('SSH连接失败');
                    return;
                }

                progress.report({ increment: 30, message: 'SSH连接成功，准备执行命令...' });
                outputChannel.appendLine('SSH连接建立成功');

                // 步骤2: 执行ls /root命令
                progress.report({ increment: 50, message: '执行 ls /root 命令...' });
                outputChannel.appendLine('开始执行命令: ls /root');

                const result = await sshManager.executeCommand('ls /root');

                progress.report({ increment: 90, message: '命令执行完成' });

                // 显示结果
                if (result.success) {
                    outputChannel.appendLine('=== 命令执行结果 ===');
                    outputChannel.appendLine(result.output);
                    outputChannel.appendLine('=== 结果结束 ===');

                    vscode.window.showInformationMessage(
                        `命令执行成功！查看输出通道获取详细结果。`,
                        '查看输出'
                    ).then(selection => {
                        if (selection === '查看输出') {
                            outputChannel.show();
                        }
                    });
                } else {
                    const errorMsg = result.timedOut ? '命令执行超时' : (result.error || '未知错误');
                    outputChannel.appendLine(`命令执行失败: ${errorMsg}`);
                    vscode.window.showErrorMessage(`命令执行失败: ${errorMsg}`);
                }

                progress.report({ increment: 100, message: '完成' });
            });

        } catch (error) {
            const errorMsg = `操作失败: ${error}`;
            outputChannel.appendLine(errorMsg);
            vscode.window.showErrorMessage(errorMsg);
        }
    });

    // 注册命令：显示调试信息
    const debugCommand = vscode.commands.registerCommand('ssh-demo.showDebugInfo', () => {
        try {
            const debugInfo = sshManager.getDebugInfo();
            const debugText = JSON.stringify(debugInfo, null, 2);

            outputChannel.appendLine('\n=== 调试信息 ===');
            outputChannel.appendLine(debugText);
            outputChannel.appendLine('=== 调试信息结束 ===');
            outputChannel.show();

            vscode.window.showInformationMessage('调试信息已输出到输出通道');
        } catch (error) {
            vscode.window.showErrorMessage(`获取调试信息失败: ${error}`);
        }
    });

    // 添加命令到上下文
    context.subscriptions.push(connectCommand);
    context.subscriptions.push(debugCommand);
    context.subscriptions.push(outputChannel);

    // 监听配置变化
    const configWatcher = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('sshDemo')) {
            outputChannel.appendLine('配置已更改，重新加载设置...');
            // 这里可以重新加载配置
        }
    });
    context.subscriptions.push(configWatcher);

    outputChannel.appendLine('所有命令已注册完成');
    outputChannel.appendLine('使用 Ctrl+Shift+P 打开命令面板，搜索 "SSH Demo" 开始使用');
}

/**
 * 插件停用函数
 */
export function deactivate() {
    if (sshManager) {
        sshManager.disconnect();
    }

    if (outputChannel) {
        outputChannel.appendLine('=== SSH Shell Integration Demo 停用 ===');
        outputChannel.dispose();
    }

    console.log('SSH Shell Integration Demo 插件已停用');
}
