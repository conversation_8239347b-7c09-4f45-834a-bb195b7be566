import * as vscode from 'vscode';

/**
 * 插件激活函数
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('扩展已激活');

    // 注册一个简单的Hello World命令
    const disposable = vscode.commands.registerCommand('extension.helloWorld', () => {
        vscode.window.showInformationMessage('Hello World from VSCode Extension!');
    });

    context.subscriptions.push(disposable);
}

/**
 * 插件停用函数
 */
export function deactivate() {
    console.log('扩展已停用');
}
