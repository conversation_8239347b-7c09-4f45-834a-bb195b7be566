import * as vscode from 'vscode';

/**
 * Shell Integration 辅助类
 * 处理 VSCode Shell Integration 的开始和结束标记
 */
export class ShellIntegrationHelper {
    private static readonly COMMAND_START_MARKER = '\x1b]633;B\x07';
    private static readonly COMMAND_END_MARKER = '\x1b]633;D\x07';
    private static readonly COMMAND_EXECUTED_MARKER = '\x1b]633;C\x07';
    
    private debugEnabled: boolean;
    private outputChannel: vscode.OutputChannel;

    constructor(outputChannel: vscode.OutputChannel) {
        this.outputChannel = outputChannel;
        this.debugEnabled = vscode.workspace.getConfiguration('sshDemo').get('enableDebugLogging', true);
    }

    /**
     * 检查文本中是否包含命令结束标记
     */
    public hasCommandEndMarker(text: string): boolean {
        const hasMarker = text.includes(ShellIntegrationHelper.COMMAND_END_MARKER);
        if (this.debugEnabled && hasMarker) {
            this.log(`检测到命令结束标记: ${this.escapeControlChars(text)}`);
        }
        return hasMarker;
    }

    /**
     * 检查文本中是否包含命令开始标记
     */
    public hasCommandStartMarker(text: string): boolean {
        const hasMarker = text.includes(ShellIntegrationHelper.COMMAND_START_MARKER);
        if (this.debugEnabled && hasMarker) {
            this.log(`检测到命令开始标记: ${this.escapeControlChars(text)}`);
        }
        return hasMarker;
    }

    /**
     * 检查文本中是否包含命令执行标记
     */
    public hasCommandExecutedMarker(text: string): boolean {
        const hasMarker = text.includes(ShellIntegrationHelper.COMMAND_EXECUTED_MARKER);
        if (this.debugEnabled && hasMarker) {
            this.log(`检测到命令执行标记: ${this.escapeControlChars(text)}`);
        }
        return hasMarker;
    }

    /**
     * 清理文本中的 Shell Integration 标记
     */
    public cleanShellIntegrationMarkers(text: string): string {
        return text
            .replace(new RegExp(this.escapeRegex(ShellIntegrationHelper.COMMAND_START_MARKER), 'g'), '')
            .replace(new RegExp(this.escapeRegex(ShellIntegrationHelper.COMMAND_END_MARKER), 'g'), '')
            .replace(new RegExp(this.escapeRegex(ShellIntegrationHelper.COMMAND_EXECUTED_MARKER), 'g'), '');
    }

    /**
     * 转义控制字符用于显示
     */
    public escapeControlChars(text: string): string {
        return text
            .replace(/\x1b/g, '\\x1b')
            .replace(/\x07/g, '\\x07')
            .replace(/\r/g, '\\r')
            .replace(/\n/g, '\\n');
    }

    /**
     * 转义正则表达式特殊字符
     */
    private escapeRegex(text: string): string {
        return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 记录调试信息
     */
    public log(message: string): void {
        if (this.debugEnabled) {
            const timestamp = new Date().toISOString();
            this.outputChannel.appendLine(`[${timestamp}] ShellIntegration: ${message}`);
        }
    }

    /**
     * 分析终端输出，提取命令结果
     */
    public analyzeTerminalOutput(output: string): {
        hasStartMarker: boolean;
        hasEndMarker: boolean;
        hasExecutedMarker: boolean;
        cleanOutput: string;
        rawOutput: string;
    } {
        const analysis = {
            hasStartMarker: this.hasCommandStartMarker(output),
            hasEndMarker: this.hasCommandEndMarker(output),
            hasExecutedMarker: this.hasCommandExecutedMarker(output),
            cleanOutput: this.cleanShellIntegrationMarkers(output),
            rawOutput: output
        };

        if (this.debugEnabled) {
            this.log(`输出分析结果: ${JSON.stringify({
                hasStartMarker: analysis.hasStartMarker,
                hasEndMarker: analysis.hasEndMarker,
                hasExecutedMarker: analysis.hasExecutedMarker,
                outputLength: output.length,
                cleanOutputLength: analysis.cleanOutput.length
            }, null, 2)}`);
        }

        return analysis;
    }
}
