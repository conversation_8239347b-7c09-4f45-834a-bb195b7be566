{
    "typescript.preferences.includePackageJsonAutoImports": "on",
    "typescript.suggest.autoImports": true,
    "typescript.updateImportsOnFileMove.enabled": "always",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    "files.exclude": {
        "out": false,
        "node_modules": true,
        "**/.git": true,
        "**/.DS_Store": true
    },
    "search.exclude": {
        "out": true,
        "node_modules": true
    },
    "typescript.tsc.autoDetect": "on",
    "npm.enableScriptExplorer": true,
    "debug.console.fontSize": 14,
    "debug.console.lineHeight": 20,
    // 自动重载相关配置
    "typescript.preferences.includePackageJsonAutoImports": "on",
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/.git/**": true,
        "**/out/**": false
    },
    "typescript.referencesCodeLens.enabled": true,
    "typescript.implementationsCodeLens.enabled": true,
    // SSH Demo 插件的默认配置（用于测试）
    "sshDemo.defaultHost": "***********",
    "sshDemo.defaultUser": "root",
    "sshDemo.commandTimeout": 30000,
    "sshDemo.enableDebugLogging": true,
    // 终端配置
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell"
        },
        "Command Prompt": {
            "path": [
                "${env:windir}\\Sysnative\\cmd.exe",
                "${env:windir}\\System32\\cmd.exe"
            ],
            "args": [],
            "icon": "terminal-cmd"
        }
    }
}