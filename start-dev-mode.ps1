# 开发模式启动脚本 - 支持自动重载
Write-Host "=== SSH Shell Integration 开发模式 ===" -ForegroundColor Green

# 检查当前目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误：请在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "📁 当前目录正确" -ForegroundColor Green

# 步骤1：安装依赖
Write-Host "`n🔧 步骤1：检查并安装依赖..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "正在安装依赖..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} else {
    Write-Host "✅ 依赖已存在" -ForegroundColor Green
}

# 步骤2：初始编译
Write-Host "`n🔨 步骤2：初始编译..." -ForegroundColor Yellow
npm run compile
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 编译失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}
Write-Host "✅ 初始编译成功" -ForegroundColor Green

# 步骤3：启动Watch模式
Write-Host "`n👀 步骤3：启动TypeScript Watch模式..." -ForegroundColor Yellow
Write-Host "这将在后台监听文件变化并自动编译" -ForegroundColor Cyan

# 在新的PowerShell窗口中启动watch模式
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; npm run watch; Write-Host 'TypeScript Watch模式已启动' -ForegroundColor Green"

Start-Sleep -Seconds 2

# 步骤4：启动说明
Write-Host "`n🚀 开发模式已启动！" -ForegroundColor Green
Write-Host "`n📋 现在你可以：" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 在VSCode中选择调试配置：'Run Extension (Watch Mode)'" -ForegroundColor White
Write-Host "2. 按F5启动调试" -ForegroundColor White
Write-Host "3. 修改src/目录下的任何.ts文件" -ForegroundColor White
Write-Host "4. 文件会自动编译到out/目录" -ForegroundColor White
Write-Host "5. 在扩展开发主机窗口按Ctrl+R重新加载扩展" -ForegroundColor White
Write-Host ""

Write-Host "🔄 自动重载工作流程：" -ForegroundColor Cyan
Write-Host "修改代码 → 自动编译 → 按Ctrl+R重载 → 测试新功能" -ForegroundColor Gray

Write-Host "`n💡 开发技巧：" -ForegroundColor Yellow
Write-Host "• 保持Watch模式窗口打开" -ForegroundColor Gray
Write-Host "• 观察编译输出确认无错误" -ForegroundColor Gray
Write-Host "• 使用Ctrl+R而不是重启整个调试会话" -ForegroundColor Gray
Write-Host "• 查看'SSH Demo Debug'输出通道获取日志" -ForegroundColor Gray

Write-Host "`n🛠️ 调试配置：" -ForegroundColor Cyan
Write-Host "• 普通模式：'Run Extension'" -ForegroundColor Gray
Write-Host "• 开发模式：'Run Extension (Watch Mode)'" -ForegroundColor Gray

Write-Host "`n📊 监控信息：" -ForegroundColor Cyan
Write-Host "• TypeScript编译：Watch模式窗口" -ForegroundColor Gray
Write-Host "• 扩展日志：SSH Demo Debug输出通道" -ForegroundColor Gray
Write-Host "• 调试信息：调试控制台" -ForegroundColor Gray

Write-Host "`n✅ 开发环境准备完成！" -ForegroundColor Green
Write-Host "现在可以开始高效的开发和调试了！" -ForegroundColor White

Read-Host "`n按任意键退出"
