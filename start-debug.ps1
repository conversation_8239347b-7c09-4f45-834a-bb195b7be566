# SSH Shell Integration Demo 启动脚本
# 用于快速启动插件调试

Write-Host "=== SSH Shell Integration Demo 启动脚本 ===" -ForegroundColor Green

# 检查是否在正确的目录
if (-not (Test-Path "package.json")) {
    Write-Host "错误：请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 检查Node.js
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Cyan
} catch {
    Write-Host "错误：未找到Node.js，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查npm
try {
    $npmVersion = npm --version
    Write-Host "npm版本: $npmVersion" -ForegroundColor Cyan
} catch {
    Write-Host "错误：未找到npm" -ForegroundColor Red
    exit 1
}

# 安装依赖
Write-Host "`n正在安装依赖..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误：依赖安装失败" -ForegroundColor Red
    exit 1
}

# 编译项目
Write-Host "`n正在编译项目..." -ForegroundColor Yellow
npm run compile

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误：编译失败" -ForegroundColor Red
    exit 1
}

# 检查编译输出
if (Test-Path "out") {
    Write-Host "编译成功！输出目录: out/" -ForegroundColor Green
} else {
    Write-Host "警告：未找到编译输出目录" -ForegroundColor Yellow
}

Write-Host "`n=== 准备完成 ===" -ForegroundColor Green
Write-Host "现在可以在VSCode中按F5启动调试模式" -ForegroundColor Cyan
Write-Host "`n使用说明："
Write-Host "1. 在VSCode中打开此项目" -ForegroundColor White
Write-Host "2. 按F5启动扩展开发主机" -ForegroundColor White
Write-Host "3. 在新窗口中按Ctrl+Shift+P" -ForegroundColor White
Write-Host "4. 搜索'SSH Demo'并选择命令" -ForegroundColor White
Write-Host "5. 查看'SSH Demo Debug'输出通道获取详细日志" -ForegroundColor White

Write-Host "`n配置文件位置: .vscode/settings.json" -ForegroundColor Gray
Write-Host "调试配置: .vscode/launch.json" -ForegroundColor Gray
