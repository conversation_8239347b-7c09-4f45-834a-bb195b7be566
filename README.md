# Basic VSCode Extension

A basic VSCode extension template.

## Features

- Simple "Hello World" command demonstration

## Usage

1. Press `F5` to run the extension in a new Extension Development Host window
2. Press `Ctrl+Shift+P` to open the command palette
3. Type "Hello World" and select the command
4. You should see a "Hello World from VSCode Extension!" message

## Development

### Prerequisites

- Node.js
- VSCode

### Setup

```bash
npm install
```

### Build

```bash
npm run compile
```

### Watch mode

```bash
npm run watch
```

## Extension Structure

- `src/extension.ts` - Main extension file
- `package.json` - Extension manifest
- `.vscode/launch.json` - Debug configuration
- `.vscode/tasks.json` - Build tasks

## License

MIT
