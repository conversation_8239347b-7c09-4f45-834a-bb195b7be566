# SSH Shell Integration Demo

这是一个演示VSCode Shell Integration功能的插件示例，展示如何通过SSH连接到远程服务器并执行命令。

## 功能特性

- 🔗 SSH连接管理
- 🖥️ Shell Integration集成
- ⏱️ 命令执行超时控制
- 🔍 详细的调试信息
- 📝 实时日志输出

## 主要组件

### 1. ShellIntegrationHelper
负责处理VSCode Shell Integration的标记符：
- 命令开始标记 (`\x1b]633;B\x07`)
- 命令结束标记 (`\x1b]633;D\x07`)
- 命令执行标记 (`\x1b]633;C\x07`)

### 2. SSHTerminalManager
管理SSH连接和命令执行：
- 创建和管理终端
- 执行SSH连接
- 在远程服务器上执行命令
- 处理超时和错误

### 3. Extension主模块
插件的入口点，注册命令和处理用户交互。

## 使用方法

### 1. 安装依赖
```bash
npm install
```

### 2. 编译项目
```bash
npm run compile
```

### 3. 调试插件
1. 在VSCode中打开项目
2. 按 `F5` 启动调试
3. 在新的VSCode窗口中使用插件

### 4. 使用插件命令
- `SSH Demo: Connect and Execute Commands` - 连接SSH并执行命令
- `SSH Demo: Show Debug Information` - 显示调试信息

## 配置选项

在VSCode设置中可以配置以下选项：

```json
{
  "sshDemo.defaultHost": "*************",
  "sshDemo.defaultUser": "root",
  "sshDemo.commandTimeout": 30000,
  "sshDemo.enableDebugLogging": true
}
```

## 调试功能

插件包含详细的调试功能：

1. **输出通道**: 所有操作都会记录到"SSH Demo Debug"输出通道
2. **Shell Integration标记检测**: 自动检测和处理Shell Integration标记
3. **超时处理**: 防止命令执行无限等待
4. **错误处理**: 完整的错误捕获和报告

## 注意事项

1. **Shell Integration标记**: 插件会检测VSCode Shell Integration的开始和结束标记
2. **超时机制**: SSH连接和命令执行都有超时保护
3. **终端管理**: 插件会创建专用的SSH终端
4. **调试信息**: 启用调试模式可以看到详细的执行过程

## 示例执行流程

1. 用户执行 `SSH Demo: Connect and Execute Commands` 命令
2. 输入SSH主机地址和用户名
3. 插件创建新终端并执行SSH连接
4. 连接成功后执行 `ls /root` 命令
5. 捕获命令输出并显示结果
6. 所有过程都有详细的日志记录

## 开发说明

这个插件主要用于学习和演示目的，展示了：
- VSCode插件开发基础
- Shell Integration API使用
- 终端管理和命令执行
- 异步操作和超时处理
- 调试和日志记录

## 扩展建议

可以基于这个示例进一步开发：
- 支持密钥认证
- 多服务器管理
- 命令历史记录
- 文件传输功能
- 更复杂的Shell Integration处理

## 故障排除

如果遇到问题：
1. 检查"SSH Demo Debug"输出通道的日志
2. 确认SSH服务器可以正常连接
3. 检查网络连接和防火墙设置
4. 验证用户名和密码是否正确
