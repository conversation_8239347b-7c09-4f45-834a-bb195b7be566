const chokidar = require('chokidar');
const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * VSCode扩展自动重载器
 * 监听编译输出变化，自动重启VSCode扩展开发主机
 */
class VSCodeAutoReloader {
    constructor() {
        this.isReloading = false;
        this.reloadTimeout = null;
        this.lastReloadTime = 0;
        this.minReloadInterval = 3000; // 最小重载间隔3秒
        this.extensionHostProcess = null;
        this.vscodeCommand = this.findVSCodeCommand();
        
        console.log('🚀 VSCode扩展自动重载器启动...');
        console.log(`📍 VSCode命令: ${this.vscodeCommand}`);
        
        this.setupWatcher();
        this.setupProcessHandlers();
    }

    findVSCodeCommand() {
        // 尝试找到VSCode命令
        const possibleCommands = [
            'code',
            'code-insiders',
            'codium',
            process.env.VSCODE_CLI_PATH
        ].filter(Boolean);

        for (const cmd of possibleCommands) {
            try {
                exec(`${cmd} --version`, (error) => {
                    if (!error) {
                        return cmd;
                    }
                });
            } catch (e) {
                // 继续尝试下一个
            }
        }

        return 'code'; // 默认使用code
    }

    setupWatcher() {
        // 监听out目录下的js文件变化
        const watcher = chokidar.watch('./out/**/*.js', {
            ignored: /node_modules/,
            persistent: true,
            ignoreInitial: true,
            awaitWriteFinish: {
                stabilityThreshold: 1000,
                pollInterval: 100
            }
        });

        watcher
            .on('add', (filePath) => {
                console.log(`📁 新文件: ${path.relative(process.cwd(), filePath)}`);
                this.scheduleReload();
            })
            .on('change', (filePath) => {
                console.log(`📝 文件更新: ${path.relative(process.cwd(), filePath)}`);
                this.scheduleReload();
            })
            .on('unlink', (filePath) => {
                console.log(`🗑️ 文件删除: ${path.relative(process.cwd(), filePath)}`);
                this.scheduleReload();
            })
            .on('error', (error) => {
                console.error(`❌ 监听错误: ${error}`);
            });

        console.log('👀 正在监听编译输出文件变化...');
        console.log('💡 修改 src/ 目录下的文件将触发自动重载');
    }

    setupProcessHandlers() {
        // 优雅退出处理
        process.on('SIGINT', () => {
            console.log('\n👋 正在关闭自动重载器...');
            this.cleanup();
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            console.log('\n👋 正在关闭自动重载器...');
            this.cleanup();
            process.exit(0);
        });
    }

    scheduleReload() {
        const now = Date.now();
        
        // 防止频繁重载
        if (now - this.lastReloadTime < this.minReloadInterval) {
            if (this.reloadTimeout) {
                clearTimeout(this.reloadTimeout);
            }
            
            const delay = this.minReloadInterval - (now - this.lastReloadTime);
            console.log(`⏳ 延迟 ${Math.round(delay/1000)}秒 后重载...`);
            
            this.reloadTimeout = setTimeout(() => {
                this.performReload();
            }, delay);
            
            return;
        }

        this.performReload();
    }

    async performReload() {
        if (this.isReloading) {
            console.log('⚠️ 重载正在进行中，跳过此次请求');
            return;
        }

        this.isReloading = true;
        this.lastReloadTime = Date.now();

        console.log('\n🔄 检测到编译输出变化，开始自动重载...');

        try {
            // 方法1: 尝试发送重载命令到现有的VSCode窗口
            await this.sendReloadCommand();
            console.log('✅ 扩展重载完成');
        } catch (error) {
            console.log('⚠️ 发送重载命令失败，尝试重启扩展开发主机...');
            
            try {
                // 方法2: 重启扩展开发主机
                await this.restartExtensionHost();
                console.log('✅ 扩展开发主机重启完成');
            } catch (restartError) {
                console.error('❌ 自动重载失败:', restartError.message);
                console.log('💡 请手动重载扩展或重启调试会话');
            }
        }

        this.isReloading = false;
        console.log('📍 等待下次文件变化...\n');
    }

    async sendReloadCommand() {
        return new Promise((resolve, reject) => {
            // 尝试发送重载命令到VSCode
            const commands = [
                // Windows PowerShell方法
                `powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('^r')"`,
                // VSCode CLI方法
                `${this.vscodeCommand} --command "workbench.action.reloadWindow"`,
                // 备用方法
                `echo "请在扩展开发主机窗口按 Ctrl+R 重载扩展"`
            ];

            let commandIndex = 0;

            const tryNextCommand = () => {
                if (commandIndex >= commands.length - 1) {
                    reject(new Error('所有自动重载方法都失败了'));
                    return;
                }

                const command = commands[commandIndex];
                console.log(`🔧 尝试重载方法 ${commandIndex + 1}...`);

                exec(command, { timeout: 5000 }, (error, stdout, stderr) => {
                    if (error && commandIndex < commands.length - 2) {
                        console.log(`⚠️ 方法 ${commandIndex + 1} 失败，尝试下一个...`);
                        commandIndex++;
                        setTimeout(tryNextCommand, 500);
                    } else {
                        console.log(`📤 重载命令已发送`);
                        resolve();
                    }
                });
            };

            tryNextCommand();
        });
    }

    async restartExtensionHost() {
        return new Promise((resolve, reject) => {
            console.log('🔄 正在重启扩展开发主机...');
            
            // 这里可以实现更复杂的重启逻辑
            // 目前只是发送通知
            console.log('💡 请手动重启调试会话以获得最佳体验');
            
            setTimeout(() => {
                resolve();
            }, 1000);
        });
    }

    cleanup() {
        if (this.reloadTimeout) {
            clearTimeout(this.reloadTimeout);
        }
        
        if (this.extensionHostProcess) {
            this.extensionHostProcess.kill();
        }
    }
}

// 启动自动重载器
console.log('🎯 启动VSCode扩展自动重载器...');
console.log('📋 功能说明:');
console.log('   • 监听 out/**/*.js 文件变化');
console.log('   • 自动发送重载命令到VSCode');
console.log('   • 防止频繁重载（最小间隔3秒）');
console.log('   • 支持优雅退出（Ctrl+C）');
console.log('');

new VSCodeAutoReloader();
