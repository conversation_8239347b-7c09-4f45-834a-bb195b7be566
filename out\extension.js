"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
/**
 * 插件激活函数
 */
function activate(context) {
    console.log('扩展已激活');
    // 注册一个简单的Hello World命令
    const disposable = vscode.commands.registerCommand('extension.helloWorld', () => {
        vscode.window.showInformationMessage('Hello World from VSCode Extension!');
    });
    context.subscriptions.push(disposable);
}
exports.activate = activate;
/**
 * 插件停用函数
 */
function deactivate() {
    console.log('扩展已停用');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map