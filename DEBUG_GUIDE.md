# VSCode插件调试详细指南

## 🚨 解决 "Could not find the task" 错误

### 问题原因
VSCode找不到编译任务，通常是因为：
1. 任务配置路径错误
2. npm脚本未正确配置
3. 项目未正确编译

### 解决步骤

#### 步骤1：确保项目已编译
```bash
# 在项目根目录运行
npm install
npm run compile
```

#### 步骤2：检查编译输出
确保 `out` 目录存在且包含编译后的 `.js` 文件：
```
out/
├── extension.js
├── sshTerminalManager.js
├── advancedShellIntegration.js
└── shellIntegrationHelper.js
```

#### 步骤3：验证任务配置
检查 `.vscode/tasks.json` 是否存在并包含正确的npm任务。

## 🔧 详细调试步骤

### 方法1：使用调试面板（推荐）

1. **打开调试面板**
   - 按 `Ctrl+Shift+D` 或点击左侧调试图标
   - 确保选择了 "Run Extension" 配置

2. **启动调试**
   - 点击绿色播放按钮 ▶️
   - 或按 `F5`

3. **处理编译任务**
   - 如果提示找不到任务，选择 "Debug Anyway"
   - 或者先手动编译：`Ctrl+Shift+P` → "Tasks: Run Task" → "npm: compile"

### 方法2：手动编译后调试

1. **手动编译**
   ```bash
   npm run compile
   ```

2. **启动调试**
   - 按 `F5`
   - 选择 "Debug Anyway" 如果仍有任务提示

### 方法3：使用命令面板

1. **打开命令面板**
   - 按 `Ctrl+Shift+P`

2. **运行调试命令**
   - 输入 "Debug: Start Debugging"
   - 选择 "Run Extension"

## 🎯 调试成功的标志

### 1. 扩展开发主机窗口打开
- 会打开一个新的VSCode窗口
- 标题栏显示 "[Extension Development Host]"
- 这个窗口就是测试环境

### 2. 插件已加载
在新窗口中：
- 按 `Ctrl+Shift+P` 打开命令面板
- 输入 "SSH Demo" 应该能看到两个命令：
  - `SSH Demo: Connect and Execute Commands`
  - `SSH Demo: Show Debug Information`

### 3. 输出通道可见
- 在新窗口中，查看 "输出" 面板
- 应该能看到 "SSH Demo Debug" 通道

## 🐛 常见问题和解决方案

### 问题1：新窗口中看不到插件命令

**原因**：插件未正确激活

**解决方案**：
1. 检查 `package.json` 中的 `activationEvents`
2. 确保编译成功
3. 在调试控制台查看错误信息

### 问题2：编译失败

**解决方案**：
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
npm run compile
```

### 问题3：TypeScript错误

**解决方案**：
1. 检查 `tsconfig.json` 配置
2. 确保所有依赖已安装
3. 运行 `npm run compile` 查看具体错误

### 问题4：插件激活失败

**检查步骤**：
1. 打开调试控制台（`Ctrl+Shift+Y`）
2. 查看是否有错误信息
3. 检查 `extension.ts` 中的 `activate` 函数

## 📊 调试工具使用

### 1. 调试控制台
- 位置：调试窗口底部
- 用途：查看插件运行时的错误和日志
- 快捷键：`Ctrl+Shift+Y`

### 2. 输出面板
- 位置：扩展开发主机窗口底部
- 选择：`SSH Demo Debug` 通道
- 用途：查看插件的详细日志

### 3. 开发者工具
在扩展开发主机窗口中：
- 按 `Ctrl+Shift+I` 打开开发者工具
- 查看 Console 标签页的错误信息

## 🔍 验证插件功能

### 测试步骤

1. **在扩展开发主机窗口中**：
   ```
   Ctrl+Shift+P → 输入 "SSH Demo: Show Debug Information"
   ```

2. **应该看到**：
   - 弹出信息提示
   - 输出通道显示调试信息

3. **测试主要功能**：
   ```
   Ctrl+Shift+P → 输入 "SSH Demo: Connect and Execute Commands"
   ```

4. **输入测试信息**：
   - SSH主机：`127.0.0.1` 或任意IP
   - 用户名：`test` 或任意用户名

5. **观察结果**：
   - 应该创建新终端
   - 输出通道显示详细日志
   - 模拟SSH连接和命令执行过程

## 🛠️ 高级调试技巧

### 1. 设置断点
- 在 `src/extension.ts` 中设置断点
- 按 `F5` 启动调试
- 在扩展开发主机中触发命令
- 断点会在原窗口中命中

### 2. 实时编辑
- 修改源代码后运行 `npm run compile`
- 在扩展开发主机窗口按 `Ctrl+R` 重新加载
- 无需重启整个调试会话

### 3. 日志调试
在代码中添加日志：
```typescript
console.log('调试信息:', data);
outputChannel.appendLine('详细日志: ' + message);
```

## 📝 调试检查清单

- [ ] 项目已正确编译（`out` 目录存在）
- [ ] 依赖已安装（`node_modules` 存在）
- [ ] 调试配置正确（`.vscode/launch.json`）
- [ ] 任务配置正确（`.vscode/tasks.json`）
- [ ] 扩展开发主机窗口已打开
- [ ] 可以在命令面板中找到插件命令
- [ ] 输出通道显示插件日志
- [ ] 没有编译错误或运行时错误

按照这个指南，你应该能够成功调试VSCode插件！
