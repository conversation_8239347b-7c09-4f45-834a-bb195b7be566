# 简化的调试启动脚本
Write-Host "=== VSCode插件调试启动 ===" -ForegroundColor Green

# 检查当前目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误：请在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "📁 当前目录正确" -ForegroundColor Green

# 步骤1：安装依赖
Write-Host "`n🔧 步骤1：检查并安装依赖..." -ForegroundColor Yellow
if (-not (Test-Path "node_modules")) {
    Write-Host "正在安装依赖..." -ForegroundColor Cyan
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} else {
    Write-Host "✅ 依赖已存在" -ForegroundColor Green
}

# 步骤2：编译项目
Write-Host "`n🔨 步骤2：编译项目..." -ForegroundColor Yellow
npm run compile
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 编译失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查编译输出
if (Test-Path "out/extension.js") {
    Write-Host "✅ 编译成功" -ForegroundColor Green
} else {
    Write-Host "❌ 编译输出不完整" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 步骤3：启动说明
Write-Host "`n🚀 步骤3：启动调试" -ForegroundColor Yellow
Write-Host "现在请按照以下步骤操作：" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 在VSCode中打开此项目" -ForegroundColor White
Write-Host "2. 按 F5 启动调试" -ForegroundColor White
Write-Host "3. 如果提示找不到任务，选择 'Debug Anyway'" -ForegroundColor White
Write-Host "4. 等待新的VSCode窗口打开（扩展开发主机）" -ForegroundColor White
Write-Host "5. 在新窗口中按 Ctrl+Shift+P" -ForegroundColor White
Write-Host "6. 输入 'SSH Demo' 查看可用命令" -ForegroundColor White
Write-Host ""
Write-Host "📊 调试信息查看：" -ForegroundColor Cyan
Write-Host "- 输出面板 → 'SSH Demo Debug' 通道" -ForegroundColor Gray
Write-Host "- 调试控制台（Ctrl+Shift+Y）" -ForegroundColor Gray
Write-Host ""
Write-Host "✅ 准备完成！现在可以开始调试了" -ForegroundColor Green

Read-Host "`n按任意键退出"
