# 真实SSH测试脚本
Write-Host "=== SSH Shell Integration 真实测试 ===" -ForegroundColor Green

Write-Host "`n📋 测试前检查清单：" -ForegroundColor Yellow
Write-Host "□ 你有一个可用的SSH服务器" -ForegroundColor White
Write-Host "□ 知道SSH服务器的IP地址" -ForegroundColor White
Write-Host "□ 有有效的用户名和密码" -ForegroundColor White
Write-Host "□ SSH服务器允许密码认证" -ForegroundColor White
Write-Host "□ 网络连接正常" -ForegroundColor White

Write-Host "`n🔧 如果没有SSH服务器，可以使用：" -ForegroundColor Cyan
Write-Host "1. WSL2: ssh username@localhost" -ForegroundColor Gray
Write-Host "2. Docker: docker run -d -p 2222:22 ubuntu/ssh" -ForegroundColor Gray
Write-Host "3. 云服务器（阿里云、腾讯云等）" -ForegroundColor Gray
Write-Host "4. 虚拟机（VirtualBox、VMware）" -ForegroundColor Gray

$continue = Read-Host "`n是否继续测试？(y/n)"
if ($continue -ne 'y' -and $continue -ne 'Y') {
    Write-Host "测试已取消" -ForegroundColor Yellow
    exit 0
}

# 编译项目
Write-Host "`n🔨 编译项目..." -ForegroundColor Yellow
npm run compile

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 编译失败" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 编译成功" -ForegroundColor Green

# 测试说明
Write-Host "`n🚀 测试步骤：" -ForegroundColor Yellow
Write-Host "1. 按F5启动调试（选择'Debug Anyway'）" -ForegroundColor White
Write-Host "2. 在新窗口中按Ctrl+Shift+P" -ForegroundColor White
Write-Host "3. 输入'SSH Demo: Connect and Execute Commands'" -ForegroundColor White
Write-Host "4. 输入你的SSH服务器信息" -ForegroundColor White
Write-Host "5. 在终端中输入密码" -ForegroundColor White
Write-Host "6. 点击'已连接'确认" -ForegroundColor White
Write-Host "7. 等待命令执行并确认完成" -ForegroundColor White

Write-Host "`n📊 观察要点：" -ForegroundColor Cyan
Write-Host "• 终端是否正确创建" -ForegroundColor Gray
Write-Host "• SSH命令是否发送" -ForegroundColor Gray
Write-Host "• 密码输入是否正常" -ForegroundColor Gray
Write-Host "• 命令执行是否成功" -ForegroundColor Gray
Write-Host "• 输出通道的日志信息" -ForegroundColor Gray
Write-Host "• Shell Integration标记检测" -ForegroundColor Gray

Write-Host "`n🔍 调试信息位置：" -ForegroundColor Cyan
Write-Host "• 输出面板 → 'SSH Demo Debug'" -ForegroundColor Gray
Write-Host "• 调试控制台（Ctrl+Shift+Y）" -ForegroundColor Gray
Write-Host "• 终端窗口的实际输出" -ForegroundColor Gray

Write-Host "`n✅ 准备完成！现在可以开始真实SSH测试" -ForegroundColor Green
Write-Host "按F5启动调试，然后按照上述步骤操作" -ForegroundColor White

Read-Host "`n按任意键退出"
