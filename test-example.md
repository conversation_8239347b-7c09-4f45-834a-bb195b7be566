# SSH Shell Integration Demo 测试示例

## 如何测试插件

### 1. 启动调试模式

1. 在VSCode中打开项目
2. 按 `F5` 启动调试模式
3. 这会打开一个新的VSCode扩展开发主机窗口

### 2. 使用插件命令

在新窗口中：

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "SSH Demo" 查看可用命令：
   - `SSH Demo: Connect and Execute Commands` - 主要功能
   - `SSH Demo: Show Debug Information` - 显示调试信息

### 3. 测试SSH连接

1. 执行 `SSH Demo: Connect and Execute Commands`
2. 输入SSH主机地址（默认：*************）
3. 输入用户名（默认：root）
4. 观察插件的执行过程

### 4. 查看调试信息

1. 插件会自动打开 "SSH Demo Debug" 输出通道
2. 所有操作都会有详细的日志记录
3. 可以执行 `SSH Demo: Show Debug Information` 查看当前状态

## 预期行为

### Shell Integration 标记检测

插件会检测以下VSCode Shell Integration标记：
- 命令开始标记：`\x1b]633;B\x07`
- 命令执行标记：`\x1b]633;C\x07`
- 命令结束标记：`\x1b]633;D\x07`

### 超时处理

- 默认超时时间：30秒
- SSH连接超时：3秒等待
- 命令执行超时：可配置

### 输出处理

1. **有Shell Integration标记**：使用标记判断命令完成
2. **无Shell Integration标记**：使用时间和输出稳定性判断

## 调试功能

### 输出通道日志

所有操作都会记录到 "SSH Demo Debug" 输出通道：

```
[2024-01-01T10:00:00.000Z] SSHManager: 尝试连接到 root@*************
[2024-01-01T10:00:01.000Z] AdvancedShellIntegration: 设置监听终端: SSH Demo - *************
[2024-01-01T10:00:02.000Z] AdvancedShellIntegration: 开始执行命令: ls /root
[2024-01-01T10:00:03.000Z] ShellIntegration: 检测到命令开始标记: \x1b]633;B\x07
[2024-01-01T10:00:04.000Z] AdvancedShellIntegration: 收到输出: total 24\ndrwxr-xr-x...
[2024-01-01T10:00:05.000Z] ShellIntegration: 检测到命令结束标记: \x1b]633;D\x07
```

### 调试信息结构

```json
{
  "isConnected": true,
  "hasTerminal": true,
  "terminalName": "SSH Demo - *************",
  "commandTimeout": 30000,
  "timestamp": "2024-01-01T10:00:00.000Z",
  "advancedShellIntegration": {
    "isMonitoring": false,
    "outputBufferLength": 256,
    "lastOutputTime": 1704110400000,
    "hasTerminal": true,
    "terminalName": "SSH Demo - *************"
  }
}
```

## 配置选项

可以在VSCode设置中修改：

```json
{
  "sshDemo.defaultHost": "*************",
  "sshDemo.defaultUser": "root", 
  "sshDemo.commandTimeout": 30000,
  "sshDemo.enableDebugLogging": true
}
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查网络连接
   - 验证SSH服务器状态
   - 确认用户名和密码

2. **命令执行超时**
   - 增加超时时间设置
   - 检查命令是否需要交互输入
   - 查看调试日志了解详情

3. **Shell Integration不工作**
   - 确认VSCode版本支持Shell Integration
   - 检查终端类型和配置
   - 查看是否有Shell Integration标记

### 调试步骤

1. 启用详细日志：`"sshDemo.enableDebugLogging": true`
2. 查看输出通道的完整日志
3. 执行调试信息命令查看状态
4. 检查终端是否正确创建和连接

## 扩展开发

基于这个示例，你可以：

1. **添加真实的终端输出监听**
   - 使用VSCode Terminal API的新功能
   - 实现更准确的输出捕获

2. **支持更多认证方式**
   - SSH密钥认证
   - 多因素认证

3. **增强命令执行**
   - 交互式命令支持
   - 批量命令执行
   - 命令历史记录

4. **改进Shell Integration**
   - 更精确的标记检测
   - 支持不同Shell类型
   - 错误状态检测

这个示例为学习VSCode Shell Integration提供了一个完整的基础框架。
