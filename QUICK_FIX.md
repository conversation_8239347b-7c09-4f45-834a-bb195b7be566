# 🚨 快速故障排除指南

## 问题：Could not find the task 'npm: compile'

### ✅ 解决方案（按顺序尝试）

#### 方案1：直接忽略任务错误
1. 按 `F5` 启动调试
2. 当出现任务错误提示时，点击 **"Debug Anyway"**
3. 等待新的VSCode窗口打开

#### 方案2：手动编译后调试
```bash
# 在终端中运行
npm run compile
```
然后按 `F5` 启动调试

#### 方案3：使用命令面板
1. 按 `Ctrl+Shift+P`
2. 输入 "Debug: Start Debugging"
3. 选择 "Run Extension"

## 🎯 验证调试是否成功

### 成功标志
1. **新窗口打开**：标题显示 `[Extension Development Host]`
2. **插件命令可用**：
   - 按 `Ctrl+Shift+P`
   - 输入 "SSH Demo"
   - 应该看到两个命令

### 如果新窗口中没有看到命令

#### 检查步骤
1. **查看调试控制台**：
   - 在原VSCode窗口底部
   - 查看是否有红色错误信息

2. **检查输出面板**：
   - 在新窗口中打开输出面板
   - 选择 "SSH Demo Debug" 通道

3. **重新加载扩展**：
   - 在新窗口中按 `Ctrl+R`

## 🔧 完整调试流程

### 步骤1：准备环境
```bash
# 运行简化脚本
.\debug-simple.ps1
```

### 步骤2：启动调试
1. 在VSCode中打开项目
2. 按 `F5`
3. 选择 "Debug Anyway"（如果提示）

### 步骤3：测试功能
在新窗口中：
1. `Ctrl+Shift+P`
2. 输入 "SSH Demo: Show Debug Information"
3. 应该看到成功提示

### 步骤4：测试主功能
1. `Ctrl+Shift+P`
2. 输入 "SSH Demo: Connect and Execute Commands"
3. 输入任意IP和用户名
4. 观察终端创建和日志输出

## 🐛 常见问题

### Q: 新窗口打开但是空白
**A**: 插件可能未激活，检查：
- 调试控制台的错误信息
- 确保编译成功（`out` 目录存在）

### Q: 找不到命令
**A**: 插件未正确注册，尝试：
- 在新窗口按 `Ctrl+R` 重新加载
- 检查 `package.json` 中的命令配置

### Q: 编译失败
**A**: 依赖问题，运行：
```bash
rm -rf node_modules
npm install
npm run compile
```

### Q: 终端创建失败
**A**: 这是正常的，因为是演示代码，主要观察：
- 日志输出是否正常
- Shell Integration 标记检测
- 超时处理机制

## 📊 调试信息位置

### 1. 调试控制台
- **位置**：原VSCode窗口底部
- **内容**：插件加载和运行错误
- **快捷键**：`Ctrl+Shift+Y`

### 2. 输出通道
- **位置**：新VSCode窗口（扩展开发主机）
- **选择**：`SSH Demo Debug`
- **内容**：插件详细运行日志

### 3. 开发者工具
- **位置**：新VSCode窗口
- **快捷键**：`Ctrl+Shift+I`
- **内容**：JavaScript运行时错误

## 🎯 成功调试的完整日志示例

当一切正常时，你应该在输出通道看到：

```
=== SSH Shell Integration Demo 启动 ===
所有命令已注册完成
使用 Ctrl+Shift+P 打开命令面板，搜索 "SSH Demo" 开始使用
```

执行命令时：
```
--- 开始SSH连接和命令执行演示 ---
[时间戳] SSHManager: 尝试连接到 test@127.0.0.1
[时间戳] AdvancedShellIntegration: 设置监听终端: SSH Demo - 127.0.0.1
[时间戳] SSHManager: 执行SSH命令: ssh test@127.0.0.1
[时间戳] SSHManager: SSH连接已建立
[时间戳] SSHManager: 执行命令: ls /root
[时间戳] AdvancedShellIntegration: 开始执行命令: ls /root
```

## 💡 调试技巧

1. **实时修改**：修改代码后运行 `npm run compile`，然后在新窗口按 `Ctrl+R`
2. **断点调试**：在源代码中设置断点，触发命令时会在原窗口中停止
3. **日志调试**：观察输出通道的详细日志了解执行流程

按照这个指南，你应该能够成功调试插件！主要记住：即使有任务错误，选择 "Debug Anyway" 通常就能正常工作。
